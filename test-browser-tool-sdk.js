// 测试 Browser Tool SDK 的页面内容获取功能
// 这个文件可以在浏览器控制台中运行来测试功能

async function testBrowserToolSDK() {
  try {
    // 创建 SDK 实例
    const sdk = new BrowserToolSDK({
      serverUrl: 'ws://localhost:8765',
      autoConnect: false, // 不自动连接，只测试页面信息获取
      enableLogging: true
    });

    // 获取当前活动标签页
    const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
    
    if (!activeTab || !activeTab.id) {
      console.error('无法获取当前标签页');
      return;
    }

    console.log('正在获取页面信息...', activeTab.title);

    // 测试页面信息获取
    const pageInfo = await sdk.getPageInfo(activeTab.id, {
      include_text: true,
      include_dom: false, // 只测试文本内容
      max_text_length: 2000 // 限制长度便于查看
    });

    console.log('页面信息获取成功:');
    console.log('标题:', pageInfo.title);
    console.log('URL:', pageInfo.url);
    console.log('域名:', pageInfo.domain);
    console.log('文本内容长度:', pageInfo.text?.length);
    console.log('文本内容预览:', pageInfo.text?.substring(0, 500) + '...');

    // 检查是否为 Markdown 格式
    const isMarkdown = pageInfo.text?.includes('#') || 
                      pageInfo.text?.includes('**') || 
                      pageInfo.text?.includes('*') ||
                      pageInfo.text?.includes('[') && pageInfo.text?.includes('](');
    
    console.log('内容格式:', isMarkdown ? 'Markdown' : '纯文本');
    
    return pageInfo;

  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 在浏览器控制台中运行：testBrowserToolSDK()
console.log('测试函数已定义，请运行: testBrowserToolSDK()');
