import React, { useState, useEffect } from 'react'
import {
  Button,
  Modal,
  Form,
  Input,
  Table,
  Select,
  message,
  Popconfirm,
} from 'antd/es'
import type { TableProps } from 'antd'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import { ImgIcon } from '@/src/common/Icons'
import logo from '@/src/common/skillIcon/marketLogo.png'
import marketSkill from '@/src/common/skillIcon/marketSkill.png'

import {
  selectBsList,
  getUserId,
  selectBsDetailById,
  saveBsConfig,
  type ListItem,
} from '@/src/common/utils/userConfigApi'

import './businessSkills.less'

const permissionData = [
  {
    label: '超级管理员',
    value: 'superAdmin',
  },
  {
    label: '管理员',
    value: 'admin',
  },
  {
    label: '用户',
    value: 'user',
  },
]
interface UserPermission {
  id: string
  name: string
  permissionType: string
  editType?: boolean
  tempPermissionType?: string
}
const BusinessSkills = () => {
  const [form] = Form.useForm()
  const [formNewUser] = Form.useForm()

  const [dataList, setDataList] = useState<ListItem[]>([])
  const [id, setId] = useState('')
  const [selectId, setSelectId] = useState<string[]>([])
  const [editId, setEditId] = useState('')

  const [open, setOpen] = useState(false)
  const [userOpen, setUserOpen] = useState(false)

  const [loading, setLoading] = useState(false)
  const [userPermissions, setUserPermissions] = useState<UserPermission[]>([])
  // 获取用户技能配置数据
  const fetchData = async () => {
    try {
      const userId = await getUserId()
      const response = await selectBsList({ userId })
      setId(userId || '')
      // 确保响应是成功的
      if (
        !response ||
        typeof response !== 'object' ||
        'code' in response === false
      ) {
        throw new Error('无效的API响应')
      }

      if (response.code !== '0') {
        throw new Error(response.msg || '获取配置失败')
      }
      const bizConfigList = response.resultData || []
      setDataList(bizConfigList)
    } catch (error) {
    } finally {
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  // 编辑
  const handleEdit = async (item: any) => {
    try {
      const response = await selectBsDetailById({ id: item.id })
      if (response && response.resultData) {
        const { name, url, showUrl, superAdmin, admin, user } =
          response.resultData
        form.setFieldsValue({
          name,
          url,
          showUrl: showUrl ?? [''],
        })
        setUserPermissions([
          ...superAdmin.map((item) => ({
            ...item,
            permissionType: 'superAdmin',
          })),
          ...admin.map((item) => ({ ...item, permissionType: 'admin' })),
          ...user.map((item) => ({ ...item, permissionType: 'user' })),
        ])
      }
    } catch (error) {
      console.error('Error:', error)
    }
    setOpen(true)
    setEditId(item.id)
  }

  // 取消
  const handleCancel = () => {
    form.resetFields()
    setUserPermissions([])
    setSelectId([])
    setEditId('')
    setOpen(false)
  }

  // 提交
  const onFinish = async (values: any) => {
    setLoading(true)
    try {
      const { name, url, showUrl } = values
      const param = {
        id: editId || '',
        name,
        url,
        showUrl: showUrl,
        superAdmin: userPermissions.filter(
          (item) => item.permissionType === 'superAdmin'
        ),
        admin: userPermissions.filter(
          (item) => item.permissionType === 'admin'
        ),
        user: userPermissions.filter((item) => item.permissionType === 'user'),
      }
      const response = await saveBsConfig(param)
      if (response.code === '0') {
        message.success('保存成功')
        handleCancel()
        fetchData()
      } else {
        message.error(response.msg || '保存失败')
      }
    } catch (error) {
    } finally {
      setLoading(false)
    }
  }

  // 删除用户
  const deleteUser = () => {
    if (selectId.length === 0) {
      message.error('请至少选择一条数据！')
      return
    }

    setUserPermissions((prev) =>
      prev.filter((user) => !selectId.includes(user.id))
    )

    // 清空选中状态
    setSelectId([])
  }

  // 添加用户
  const addUser = async () => {
    setUserOpen(true)
  }

  // 表格列编辑用户逻辑
  const handleEditUser = (record: UserPermission) => {
    setUserPermissions((prev) =>
      prev.map((user) =>
        user.id === record.id ? { ...user, editType: true } : user
      )
    )
  }
  // 表格列删除用户逻辑
  const handleDeleteUser = (record: UserPermission) => {
    setUserPermissions((prev) => prev.filter((user) => user.id !== record.id))
  }
  // 表格列取消保存
  const handleCancelEdit = (record: UserPermission) => {
    setUserPermissions((prev) =>
      prev.map((user) =>
        user.id === record.id ? { ...user, editType: false } : user
      )
    )
  }

  // 表格列保存用户信息
  const handleSaveUser = (record: UserPermission) => {
    setUserPermissions((prev) =>
      prev.map((user) =>
        user.id === record.id
          ? {
              ...user,
              permissionType: record.tempPermissionType || user.permissionType,
              editType: false,
            }
          : user
      )
    )
  }

  // 用户权限配置弹窗-取消
  const handleCancelUser = () => {
    formNewUser.resetFields()
    setUserOpen(false)
  }

  // 用户权限配置弹窗-提交
  const onFinishUser = (values: any) => {
    try {
      const { name, id, permissionType } = values

      // 创建新的用户权限对象
      const newUser: UserPermission = {
        name,
        id,
        permissionType,
      }

      // 添加到用户权限列表
      setUserPermissions((prev) => [...prev, newUser])

      // 关闭弹窗并重置表单
      handleCancelUser()
    } catch (error) {
      console.error('Error:', error)
    }
  }

  useEffect(() => {
    if (userOpen) {
      formNewUser.setFieldsValue({
        permissionType: 'admin',
      })
    }
  }, [userOpen])

  // 用户权限配置列
  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '工号',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '权限类型',
      dataIndex: 'permissionType',
      key: 'permissionType',
      render: (text: string, record: UserPermission) => {
        const permission = permissionData.find((item) => item.value === text)
        if (record.editType) {
          return (
            <Select
              defaultValue={text}
              onChange={(value) => {
                // 临时保存修改的值
                record.tempPermissionType = value
              }}
              options={permissionData}
            />
          )
        }
        return permission ? permission.label : text
      },
    },
    {
      title: '操作',
      key: 'action',
      with: 120,
      render: (_, record) => (
        <div>
          {record.editType ? (
            <div>
              <Button type="link" onClick={() => handleCancelEdit(record)}>
                取消
              </Button>
              <Button type="link" onClick={() => handleSaveUser(record)}>
                保存
              </Button>
            </div>
          ) : (
            <>
              <Button type="link" onClick={() => handleEditUser(record)}>
                编辑
              </Button>
              <Popconfirm
                title="是否确认删除？"
                onConfirm={() => handleDeleteUser(record)}
                okText="确认"
                cancelText="取消"
              >
                <Button type="link">删除</Button>
              </Popconfirm>
            </>
          )}
        </div>
      ),
    },
  ]
  // 业务技能内容
  const renderBusinessSkillsTab = () => {
    return (
      <div className="market">
        <div className="market-header">
          <span className="header-title">业务技能</span>
        </div>
        <div className="skills">业务技能列表</div>
        <div className="common-skills">
          {dataList.length > 0 ? (
            dataList.map((item, index) => {
              return (
                <div className="skill-item-wrap" key={item.id}>
                  <div className="skill-item">
                    <div>{item.icon}</div>
                    <div className="skill-item-title">{item.name}</div>
                  </div>
                  <div className="skill-item-action">
                    <Button
                      style={{
                        height: 16,
                        borderRadius: 2,
                        background: '#1677FF',
                        color: '#fff',
                        fontSize: 8,
                        padding: '0 6px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: '100%',
                      }}
                      onClick={() => handleEdit(item)}
                    >
                      编辑
                    </Button>
                  </div>
                </div>
              )
            })
          ) : (
            <div className="noData">暂无数据</div>
          )}
        </div>
      </div>
    )
  }

  const rowSelection: TableProps<UserPermission>['rowSelection'] = {
    onChange: (selectedRowKeys: string[]) => {
      setSelectId(selectedRowKeys)
    },
  }
  return (
    <>
      <div className="settings-tab-page">
        <div className="settings-sidebar">
          <div className="sidebar-logo">
            <img src={logo} alt="logo" />
          </div>
          <div className="sidebar-nav">
            <div className="nav-item">
              <ImgIcon src={marketSkill} width={20} height={20} />
              业务技能
            </div>
          </div>
        </div>
        <div className="settings-main">
          <div className="main-content">{renderBusinessSkillsTab()}</div>
        </div>
      </div>
      {/* 编辑弹窗 */}
      <Modal
        open={open}
        title="编辑"
        onCancel={handleCancel}
        style={{ width: 1200 }}
        footer={null}
        destroyOnClose={true}
        width={800}
        wrapClassName="skillModal"
      >
        <Form
          layout="vertical"
          form={form}
          className="formModal"
          onFinish={onFinish}
          requiredMark={false}
          autoComplete="off"
        >
          <Form.Item
            name="name"
            label="技能名字"
            rules={[{ required: true, message: '请输入技能名字' }]}
          >
            <Input
              placeholder="请输入技能名字"
              style={{ width: 250, height: 40 }}
              size="large"
            />
          </Form.Item>
          <Form.Item
            name="url"
            label="能力url"
            rules={[{ required: true, message: '请输入能力url' }]}
          >
            <Input
              placeholder="请输入能力url"
              style={{ width: 250, height: 40 }}
              size="large"
            />
          </Form.Item>
          <Form.List name="showUrl">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <div
                    key={key}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: 8,
                    }}
                  >
                    <Form.Item
                      {...restField}
                      name={name}
                      label={name === 0 ? '技能url权限' : ''}
                      rules={[
                        { required: true, message: '请输入可以看到技能的URL' },
                      ]}
                    >
                      <Input
                        placeholder="请输入可以看到技能的URL"
                        size="large"
                        style={{ width: 250, height: 40 }}
                      />
                    </Form.Item>
                    {fields.length > 1 ? (
                      name === fields.length - 1 ? (
                        <PlusOutlined
                          onClick={() => add()}
                          style={{
                            marginLeft: 8,
                            marginBottom: name == 0 ? 0 : 30,
                          }}
                          title="添加"
                        />
                      ) : (
                        <Popconfirm
                          title="是否确认删除？"
                          onConfirm={() => remove(name)}
                          okText="确认"
                          cancelText="取消"
                        >
                          <DeleteOutlined
                            title="删除"
                            style={{
                              marginLeft: 8,
                              marginBottom: name == 0 ? 0 : 30,
                            }}
                          />
                        </Popconfirm>
                      )
                    ) : (
                      <PlusOutlined
                        onClick={() => add()}
                        style={{
                          marginLeft: 8,
                          marginBottom: name == 0 ? 0 : 30,
                        }}
                        title="添加"
                      />
                    )}
                  </div>
                ))}
              </>
            )}
          </Form.List>
        </Form>
        <div className="skillUser">
          <div className="skillLabel">技能用户权限</div>
          <div>
            <Popconfirm
              title="是否确认删除？"
              onConfirm={deleteUser}
              okText="确认"
              cancelText="取消"
            >
              <Button
                style={{
                  height: 40,
                  fontSize: 14,
                  lineHeight: '22px',
                  borderRadius: 0,
                  color: '#1677FF',
                  border: '1px solid #1677FF',
                }}
              >
                删除
              </Button>
            </Popconfirm>
            <Button
              style={{
                height: 40,
                background: '#1677FF',
                color: '#fff',
                fontSize: 14,
                lineHeight: '22px',
                marginLeft: 8,
                borderRadius: 0,
              }}
              onClick={addUser}
            >
              新增用户
            </Button>
          </div>
        </div>
        <div className="skillUserList">
          <Table
            rowKey="id"
            dataSource={userPermissions}
            columns={columns}
            scroll={{ y: 500 }}
            rowSelection={{ type: 'checkbox', ...rowSelection }}
            pagination={false}
          ></Table>
        </div>
        <div className="modal-footer">
          <Button
            style={{
              height: 40,
              borderRadius: 8,
              fontSize: 14,
              lineHeight: '22px',
              width: 200,
            }}
            onClick={handleCancel}
          >
            取消
          </Button>
          <Button
            style={{
              height: 40,
              borderRadius: 8,
              background: '#1677FF',
              color: '#fff',
              fontSize: 14,
              lineHeight: '22px',
              width: 200,
            }}
            onClick={() => form.submit()}
            loading={loading}
          >
            确定
          </Button>
        </div>
      </Modal>
      {/* 新增用户弹窗 */}
      <Modal
        open={userOpen}
        title="新增"
        onCancel={handleCancelUser}
        footer={null}
        wrapClassName="skillModal"
        width={300}
        zIndex={9999}
      >
        <Form
          layout="vertical"
          form={formNewUser}
          className="formModal"
          onFinish={onFinishUser}
          requiredMark={false}
          autoComplete="off"
        >
          <Form.Item
            name="name"
            label="姓名"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input
              placeholder="请输入姓名"
              style={{ width: 250, height: 40 }}
              size="large"
            />
          </Form.Item>
          <Form.Item
            name="id"
            label="工号"
            rules={[{ required: true, message: '请输入工号' }]}
          >
            <Input
              placeholder="请输入工号"
              style={{ width: 250, height: 40 }}
              size="large"
            />
          </Form.Item>
          <Form.Item
            name="permissionType"
            label="权限类型"
            rules={[{ required: true, message: '请选择权限类型' }]}
          >
            <Select
              allowClear
              placeholder="请选择权限类型"
              options={permissionData}
              style={{ width: 250, height: 40 }}
            />
          </Form.Item>
        </Form>
        <div className="modal-footer_user">
          <Button
            style={{
              height: 40,
              borderRadius: 8,
              fontSize: 14,
              lineHeight: '22px',
            }}
            onClick={handleCancelUser}
          >
            取消
          </Button>
          <Button
            style={{
              height: 40,
              borderRadius: 8,
              background: '#1677FF',
              color: '#fff',
              fontSize: 14,
              lineHeight: '22px',
            }}
            onClick={() => formNewUser.submit()}
            loading={loading}
          >
            确定
          </Button>
        </div>
      </Modal>
    </>
  )
}

export default BusinessSkills
