import { isExtensionPage } from '@src/common/utils'
import { EIP_CONFIG } from '@src/common/const'
import { fetchAndStoreWikiAuth } from '@src/common/utils/wikiAuth'


export const getCurrentTab = async (messageApi?: any) => {
  const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
  const currentTab = tabs[0]
  if (isExtensionPage(currentTab.url)) {
    messageApi?.open({
      type: 'error',
      content: '不支持Chrome内置页面，请在其他页面尝试',
    })
    return
  }
  return currentTab
}

// 检查登录状态函数
export const checkLoginStatus = (callback: (isLoggedIn: boolean, data: any) => void) => {
  chrome.cookies.get({ url: EIP_CONFIG.BASE_URL + '/', name: EIP_CONFIG.TOKEN_NAME }, function (cookie) {
    console.log('cookie is ', cookie)
    if (cookie && cookie.value) {
      // 根据环境使用不同的请求方式
      const apiUrl = EIP_CONFIG.AUTH_API
      const isProd = process.env.PLASMO_TAG === 'prod'
      const method = isProd ? 'GET' : 'POST' // 生产环境用GET，测试环境用POST

      console.log(`使用登录验证接口: ${apiUrl}, 方法: ${method}, 环境: ${isProd ? '生产' : '测试'}`)

      fetch(apiUrl, {
        method: method,
        credentials: 'include',
        headers: {
          'Cookie': `${EIP_CONFIG.TOKEN_NAME}=` + cookie.value
        }
      })
        .then(response => {
          // 获取响应体内容
          return response.json().then(data => {
            console.log('响应体内容:', data);
            return { response, data };
          }).catch(err => {
            // console.error('解析响应失败:', err);
            return { response, data: null };
          });
        })
        .then(async (result) => {
          const isProd = process.env.PLASMO_TAG === 'prod'
          let isSuccess = false
          let processedData = null

          if (isProd) {
            // 生产环境：处理新格式响应 {code: "0", systemId: "new-eip", msg: "success", resultData: {workId: "018745"}}
            isSuccess = result.response.ok && result.data && result.data.code === "0"
            if (isSuccess && result.data.resultData && result.data.resultData.workId) {
              // 将workId作为empId处理
              processedData = {
                empId: result.data.resultData.workId,
                workId: result.data.resultData.workId,
                // empName: result.data.resultData.empName,
                userId: result.data.resultData.workId,
                ...result.data.resultData
              }
              console.log('生产环境登录验证成功，workId作为empId:', processedData)
            }
          } else {
            // 测试环境：使用原有的老格式判断逻辑
            isSuccess = result.response.ok && result.data
            processedData = result.data
            console.log('测试环境登录验证成功，使用老格式:', processedData)
          }

          if (isSuccess && processedData) {
            // EIP验证成功后，异步获取并存储wiki认证信息（不阻塞主流程）
            // fetchAndStoreWikiAuth().catch(error => {
            //   console.error('获取Wiki认证信息失败，但不影响EIP登录:', error)
            // })

            callback(true, processedData);
          } else {
            console.log('登录验证失败，响应数据:', result.data)
            callback(false, null);
          }
        })
        .catch(err => {
          console.error('登录验证失败:', err);
          callback(false, null);
        });
    } else {
      callback(false, null);
    }
  });
}
