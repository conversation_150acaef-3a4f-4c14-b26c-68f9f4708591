import React, { useState, useEffect } from 'react'
import { Select, message } from 'antd'
import huaTechIcon from '../../../../assets/huatech.png'
import * as styles from './index.module.less'
import { checkWikiAuthStatus, triggerWikiLogin } from '@src/common/utils/wikiAuth'

// 搜索来源选项接口
interface SearchSourceOption {
  id: string
  label: string
  icon: React.ReactNode
  selected: boolean
}

// 组件 Props 接口
interface InternalCombinedSearchProps {
  value?: string[]
  onChange?: (value: string[]) => void
  className?: string
}

const InternalCombinedSearch: React.FC<InternalCombinedSearchProps> = ({
  value = [],
  onChange,
  className
}) => {
  const [selectedSources, setSelectedSources] = useState<string[]>(value)

  // 搜索来源选项数据
  const searchSources: SearchSourceOption[] = [
    // {
    //   id: 'qa',
    //   label: 'QA搜索',
    //   icon: <HomeOutlined />,
    //   selected: selectedSources.includes('qa')
    // },
    // {
    //   id: 'cloud-docs',
    //   label: '云文档',
    //   icon: <FileTextOutlined />,
    //   selected: selectedSources.includes('cloud-docs')
    // },
    // {
    //   id: 'paas-docs',
    //   label: 'PaaS文档',
    //   icon: <StarOutlined />,
    //   selected: selectedSources.includes('paas-docs')
    // },
    {
      id: 'huatech',
      label: '技术社区搜索',
      icon: <img src={huaTechIcon} alt="" />,
      selected: selectedSources.includes('huatech')
    },
    {
      id: 'wiki',
      label: 'Wiki搜索',
      icon: <img src="http://wiki.htzq.htsc.com.cn/images/logo/default-space-logo.svg" alt="" />,
      selected: selectedSources.includes('wiki')
    }
  ]

  // 处理选择变更
  const handleChange = async (newValues: string[]) => {
    // 检查是否新选择了Wiki搜索
    const hasWiki = newValues.includes('wiki')
    const previouslyHasWiki = selectedSources.includes('wiki')

    if (hasWiki && !previouslyHasWiki) {
      // 新选择了Wiki搜索，需要检测登录状态
      console.log('检测到选择Wiki搜索，开始检查认证状态...')

      try {
        const isAuthenticated = await checkWikiAuthStatus()

        if (!isAuthenticated) {
          console.log('Wiki未登录，直接打开登录页面')

          // 直接触发登录流程，无需确认
          message.info('Wiki搜索需要登录，正在为您打开登录页面...')

          try {
            const loginSuccess = await triggerWikiLogin()

            if (loginSuccess) {
              message.success('Wiki登录成功！现在可以使用Wiki搜索功能了')
              setSelectedSources(newValues)
              onChange?.(newValues)
            } else {
              message.warning('Wiki登录已取消，已移除Wiki搜索选项')
              // 登录失败或取消，从选择中移除wiki
              const filteredValues = newValues.filter(value => value !== 'wiki')
              setSelectedSources(filteredValues)
              onChange?.(filteredValues)
            }
          } catch (loginError) {
            console.error('Wiki登录过程中发生错误:', loginError)
            message.error('Wiki登录过程中发生错误，已移除Wiki搜索选项')
            // 登录出错，从选择中移除wiki
            const filteredValues = newValues.filter(value => value !== 'wiki')
            setSelectedSources(filteredValues)
            onChange?.(filteredValues)
          }
        } else {
          // 已登录，直接更新选择
          console.log('Wiki认证有效，直接更新选择')
          setSelectedSources(newValues)
          onChange?.(newValues)
        }
      } catch (error) {
        console.error('检查Wiki认证状态时发生错误:', error)
        message.error('检查Wiki登录状态失败，请稍后重试')
        // 发生错误，从选择中移除wiki
        const filteredValues = newValues.filter(value => value !== 'wiki')
        setSelectedSources(filteredValues)
        onChange?.(filteredValues)
      }
    } else {
      // 不涉及Wiki搜索的新增，或者取消Wiki搜索，直接更新
      setSelectedSources(newValues)
      onChange?.(newValues)
    }
  }

  return (
    <div className={`${styles.searchSourceContainer} ${className || ''}`}>
      <Select
        className={styles.select}
        mode="multiple"
        suffixIcon={selectedSources.length > 0 ? (
          <span className={`${styles.suffixText} ant-select-selection-placeholder`}>搜索来源</span>
        ) : null}
        allowClear={false}
        removeIcon={null}
        filterOption={false}
        placeholder="搜索来源"
        value={selectedSources}
        onChange={handleChange}
        maxTagCount={2}
        maxTagPlaceholder={`+${selectedSources.length}`}
        options={searchSources.map((option) => ({
          value: option.id,
          label: (
            <div className={styles.optionContent}>
              <span className={styles.optionIcon}>
                {option.icon}
              </span>
              <span className={styles.optionLabel}>{option.label}</span>
            </div>
          )
        }))}
        tagRender={(tagProps) => {
          const { value } = tagProps
          const option = searchSources.find((o) => o.id === value)
          if (!option) return null
          return (
            <span className={styles.tagIcon} >
              {option.icon}
            </span>
          )
        }}
        dropdownMatchSelectWidth={false}
      />
    </div>
  )
}

export default InternalCombinedSearch
