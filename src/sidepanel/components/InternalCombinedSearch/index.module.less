/* InternalCombinedSearch 组件样式 */

.searchSourceContainer {
  position: relative;
  display: inline-block;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Antd Select 覆盖与对齐原设计 */
.select {
  min-width: 130px;
  height: 32px;
}

.select :global(.ant-select-selector) {
  // padding: 6px 32px 6px 12px;
  border-radius: 18px !important;
  display: flex;
  align-items: center;
}

.select :global(.ant-select-selection-placeholder) {
  color: #333333;
  display: inline-flex;
  align-items: center;
}

.select :global(.ant-select-selection-item) {
  display: flex;
  align-items: center;
}

// 选中项图标样式 - 只显示图标，叠放效果
.tagIcon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 14px;
  cursor: pointer;
  border-radius: 50%;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.9);
  position: relative;
  z-index: 1;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }

  img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    display: block;
  }
}

// Ant Design Select 选中项的叠放效果
.select :global(.ant-select-selection-overflow-item:not(.ant-select-selection-overflow-item-rest):not(.ant-select-selection-overflow-item-suffix)) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 14px;
  cursor: pointer;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }

  /* 叠放效果：每个后续图标向左偏移，形成叠放 */
  &:nth-child(2) {
    margin-left: -8px;
    z-index: 2;
  }

  &:nth-child(3) {
    margin-left: -8px;
    z-index: 3;
  }

  &:nth-child(4) {
    margin-left: -8px;
    z-index: 4;
  }

  &:nth-child(5) {
    margin-left: -8px;
    z-index: 5;
  }

  &:nth-child(n+6) {
    margin-left: -8px;
    z-index: 6;
  }
}

// 隐藏搜索输入框
.select :global(.ant-select-selection-search) {
  display: none !important;
}

// 确保点击整个选择器能调起下拉框
.select :global(.ant-select-selector) {
  cursor: pointer;
}

// 自定义placeholder样式
.customPlaceholder {
  position: absolute;
  right: 32px;
  top: 50%;
  transform: translateY(-50%);
  color: #999999;
  font-size: 14px;
  pointer-events: none;
  z-index: 10;
}

// 搜索来源文案样式 - 与placeholder样式保持一致
.searchSourceText {
  position: absolute;
  right: 32px;
  top: 50%;
  transform: translateY(-50%);
}

// 自定义后缀文案样式（不使用圆角、不像tag）
.suffixText {
  color: #999999;
  font-size: 14px;
  line-height: 1;
  display: inline-flex;
  align-items: center;
}

// 覆盖默认overflow item（tag）的圆角风格，避免影响suffix
.select :global(.ant-select-selection-overflow) {
  gap: 4px;
  margin-top: -4px;
}

.select :global(.ant-select-selection-overflow-item:not(.ant-select-selection-overflow-item-rest):not(.ant-select-selection-overflow-item-suffix)) {
  border-radius: 50%;
}

// 让 suffixIcon 显示在右侧，并与文字对齐
.select :global(.ant-select-arrow) {
  display: inline-flex;
  align-items: center;
  width: 100px;
  margin-right: -20px;
}

/* 触发按钮样式 */
.triggerButton {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  min-width: 120px;
  font-size: 14px;
  color: #333333;
  transition: all 0.2s ease;

  &:hover {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
  }

  &:focus {
    outline: none;
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

.triggerText {
  color: #333333;
  font-weight: 400;
}

.triggerArrow {
  color: #999999;
  font-size: 12px;
  transition: transform 0.2s ease;

  &.open {
    transform: rotate(180deg);
  }
}

/* 下拉面板样式 */
.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  z-index: 1000;
  margin-top: 4px;
  min-width: 220px;
  animation: dropdownSlideIn 0.2s ease-out;
  overflow: hidden;
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 下拉面板头部 */
.dropdownHeader {
  padding: 12px 16px 8px;
  border-bottom: 1px solid #f0f0f0;
}

.headerTitle {
  font-size: 14px;
  font-weight: 500;
  color: #666666;
}

/* 选项列表 */
.optionsList {
  padding: 8px 0;
}

.optionItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
  }

  &.selected {
    background-color: rgba(24, 144, 255, 0.06);
  }
}

.optionContent {
  display: flex;
  align-items: center;
  flex: 1;
}

.optionIcon {
  font-size: 16px;
  margin-right: 12px;
  width: 20px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    max-width: 100%;
  }

}

.optionLabel {
  font-size: 14px;
  color: #333333;
  font-weight: 400;
}

.checkIcon {
  color: #1890ff;
  font-size: 14px;
  font-weight: bold;
}

/* 底部图标区域 */
.bottomIcons {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
  border-radius: 0 0 8px 8px;
  gap: 8px;
}

.bottomIcon {
  font-size: 14px;
  color: #666666;
  cursor: pointer;
  padding: 4px;
  transition: color 0.2s ease;
  border-radius: 4px;

  &:hover {
    color: #1890ff;
    background-color: rgba(24, 144, 255, 0.1);
  }
}

.bottomIconArrow {
  font-size: 12px;
  color: #999999;
  margin-left: 4px;
}