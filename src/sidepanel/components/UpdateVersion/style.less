.updateVersionContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
  text-align: center;

  .updateImg {
    width: 160px;
    height: 160px;
    margin-bottom: 20px;
  }

  .title {
    font-size: 18px;
    font-weight: 500;
    color: #333333;
    margin: 0 0 20px 0;
  }

  .description {
    max-width: 80%;
    margin: 0 0 20px 0;
    text-align: left;
    color: #666;
    line-height: 1.6;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin-top: 1em;
      margin-bottom: 0.5em;
      color: #333;
    }

    p {
      margin: 0.5em 0;
    }

    ul,
    ol {
      padding-left: 20px;
      margin: 0.5em 0;
    }

    ul {
      list-style-type: disc;
    }

    ol {
      list-style-type: decimal;
    }

    li {
      margin-bottom: 0.3em;
    }
  }

  .buttonContainer {
    display: flex;
    gap: 15px;
    margin-top: 10px;
  }

  .downloadBtn {
    width: 200px;
    height: 40px;
    background: #1890ff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  .skipBtn {
    width: 200px;
    height: 40px;
    background: #1890ff;
    color: #fff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    cursor: pointer;
    border: 1px solid #ddd;

    &:hover {
      opacity: 0.8;
    }
  }
}