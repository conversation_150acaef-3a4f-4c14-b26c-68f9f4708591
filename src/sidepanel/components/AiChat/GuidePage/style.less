@import '../../../../common/css/var.less';

.guide-page {
  display: flex;
  flex-direction: column;
  color: @body-color;
  font-family: @font-family-base;
  line-height: @line-height-base;
  -webkit-tap-highlight-color: transparent;
  background: @guide-page-bg;
  position: relative;
  border-radius: inherit;
  flex: 1;

  p {
    margin: 0;
  }

  .top-content {
    // width: 100%;
    margin: 56px 16px 26px 16px;
    padding: 16px;
    // height: 222px;
    background: linear-gradient(321deg, #deedff 0%, #def2ff 27%, #f5f8ff 100%);
    border-radius: 24px 24px 24px 24px;
    opacity: 1;
    border: 1px solid #d1ebff;
    position: relative;

    .title {
      font-size: 30px;
      line-height: 30px;
      font-weight: 600;
      color: #1d222c;
    }

    .chat-logo {
      position: absolute;
      width: 100px;
      height: 105px;
      top: -20px;
      right: 48px;

      @media screen and (max-width: 500px) {
        top: -20px;
        right: 26px;
      }
    }
  }

  .chat-banners {
    display: flex;
    margin: 0 16px;
    gap: 8px;

    // padding: 0 16px;
    @media screen and (max-width: 500px) {
      flex-wrap: wrap;
    }

    @media screen and (min-width: 501px) {
      flex-direction: row;
      align-items: space-between;
      flex-wrap: nowrap;
    }

    .chat-banner {
      flex: 1;
      // min-width: 200px;
      padding: 8px;
      background: linear-gradient(321deg,
          #ebf2ff 4%,
          #e0f6ff 31%,
          #eaf4ff 100%);
      border-radius: 8px 8px 8px 8px;
      opacity: 1;
      border: 1px solid #d1ebff;

      img {
        width: 19px;
        height: 17px;
        margin-bottom: 4px;
      }

      .chat-banner-title {
        font-size: 16px;
        font-weight: 600;
      }

      .chat-banner-desc {
        font-size: 11px;
        font-weight: 400;
        color: #6c6f76;
      }

      @media screen and (max-width: 500px) {
        margin: 16px;
        flex: 1 1 100%;
        margin: 0 !important;
      }

      // @media screen and (min-width: 501px) {
      //   flex: 1 1 calc(33.333% - 11px);
      // }
    }
  }

  .chat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .center-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .chat-logo {
        width: 98px;
        height: 98px;
      }

      .first-title {
        margin-top: 25px;
        margin-bottom: 12px;
        font-size: 20px;
        font-weight: bold;
        color: @guide-page-title-color;
        line-height: 27px;
      }

      .sub-title {
        font-size: 14px;
        color: @guide-page-subtitle-color;
        line-height: 19px;
      }
    }
  }

  .settings-link {
    text-align: center;
    color: #1890ff;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    vertical-align: baseline;
    line-height: inherit;
  }
}