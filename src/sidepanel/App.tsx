import Routing from './routes'
import './style.less'
import { useVersion } from '@/src/common/utils/version'
import UpdateVersion from '@src/sidepanel/components/UpdateVersion'
import React from 'react'

export default () => {
  const { isLatestVersion, latestVersion, updateType, description } = useVersion()

  if (!isLatestVersion) {
    return (
      <UpdateVersion 
        latestVersion={latestVersion}
        updateType={updateType}
        description={description}
      />
    )
  }
  return (
    <div className="side-panel">
      <Routing />
    </div>
  )
}