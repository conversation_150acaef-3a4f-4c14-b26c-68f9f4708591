import { buildApi } from './api'
import { userStorage } from './userStorage'
import { getBaseUrl } from './baseUrl'

// 用户配置数据类型定义
export interface UserAssistantConfig {
    userId: string
    floatingDisabledWebsites: string[]
    selectionDisabledWebsites: string[]
    translationDefaultTargetLanguage: string
}

// API响应类型
export interface ApiResponse<T> {
    code: string
    msg: string
    resultData: T
}

// 获取用户配置的请求参数
export interface GetUserConfigRequest {
    userId: string
}

// 保存用户配置的请求参数
export interface SaveUserConfigRequest {
    userId: string
    floatingDisabledWebsites?: string[]
    selectionDisabledWebsites?: string[]
    translationDefaultTargetLanguage?: string
}
//保存用户技能请求参数
export interface SaveUserSkillsParams {
    createUserId: string
    name: string
    fileId: string
    prompt: string
    description: string
    isConfig: number
    status: number
    id?: string
    allowUploadFile?: string | boolean
    isShowInBar?: boolean
}

interface SaveUserSkillsParamsList {
    skillConfigInfos: SaveUserSkillsParams[]
}
// 获取用户配置接口
export const getUserAssistantConfig = buildApi<
    GetUserConfigRequest,
    ApiResponse<UserAssistantConfig>
>('POST', '/web/assistant/userConfig/getUserAssistantConfig', {
    baseUrl: getBaseUrl(),
    transform: (res) => res as ApiResponse<UserAssistantConfig>,
})

// 保存用户配置接口
export const saveUserAssistantConfig = buildApi<
    SaveUserConfigRequest,
    ApiResponse<UserAssistantConfig>
>('POST', '/web/assistant/userConfig/saveUserAssistantConfig', {
    baseUrl: getBaseUrl(),
    transform: (res) => res as ApiResponse<UserAssistantConfig>,
})

// 获取用户ID的工具函数
export const getUserId = async (): Promise<string> => {
    try {
        const userInfo = await userStorage.getUserInfo()

        // 添加调试日志
        console.log('[getUserId] 从storage获取的用户信息:', userInfo)

        // 检查userInfo是否存在
        if (!userInfo || Object.keys(userInfo).length === 0) {
            console.warn('[getUserId] 用户信息为空，返回anonymous_user')
            return 'anonymous_user'
        }

        // 优先使用userId字段（检查是否为非空字符串）
        if (userInfo.userId && userInfo.userId.trim() !== '') {
            console.log('[getUserId] 使用userId:', userInfo.userId)
            return userInfo.userId
        }

        // 其次尝试使用id字段
        if (userInfo.id && userInfo.id.trim() !== '') {
            console.log('[getUserId] 使用id:', userInfo.id)
            return userInfo.id
        }

        // 尝试使用userName作为fallback
        if (userInfo.userName && userInfo.userName.trim() !== '') {
            console.log('[getUserId] 使用userName作为fallback:', userInfo.userName)
            return userInfo.userName
        }

        // 如果都没有有效值，返回默认值
        console.warn('[getUserId] 所有字段都为空，返回anonymous_user')
        return 'anonymous_user'
    } catch (error) {
        console.error('[getUserId] 获取用户ID失败:', error)
        return 'anonymous_user'
    }
}

// 查询用户技能
export const selectUserKills = buildApi<
    { createUserId: string },
    ApiResponse<any>
>('POST', '/web/assistant/skill/getConfigByCreateUserId', {
    baseUrl: getBaseUrl(),
    transform: (res) => res as ApiResponse<any>,
})


// 保存用户技能
export const saveUserSkills = buildApi<
    SaveUserSkillsParamsList,
    ApiResponse<SaveUserSkillsParams>
>('POST', '/web/assistant/skill/saveConfigList', {
    baseUrl: getBaseUrl(),
    transform: (res) => res as ApiResponse<SaveUserSkillsParams>,
})

interface AgentResult {
    choices: {
        message: {
            content: string
        }
    }[]
}
// 一件优化Prompt
export const optimizePrompt = buildApi<
    { question: string, stream: boolean, model: string },
    ApiResponse<AgentResult>
>('POST', '/agent/api/v1/prompt-optimizer', {
    baseUrl: getBaseUrl(),
    transform: (res) => res as ApiResponse<AgentResult>,
    timeout: 1000 * 60 * 60
})

// 删除用户技能
export const deleteUserKills = buildApi<
    { id: string },
    ApiResponse<any>
>('POST', '/web/assistant/skill/deleteConfigById', {
    baseUrl: getBaseUrl(),
    transform: (res) => res as ApiResponse<any>,
})


// -------------- 以下为业务技能列表接口 -----------------
// 获取业务技能列表

export interface ListItem {
    id: number,
    name: string,
    icon: string,
}

export const selectBsList = buildApi<
    { userId: string },
    ApiResponse<ListItem[]>
>('POST', '/web/assistant/bizConfig/getConfigList', {
    baseUrl: getBaseUrl(),
    transform: (res) => res as ApiResponse<ListItem[]>,
})

interface EmployeeInfo {
    id: string,
    name: string,
    permissionType: string,
}
interface DetailItem {
    id: number,
    name: string,
    url: string,
    showUrl: string[],
    superAdmin: EmployeeInfo[],
    admin: EmployeeInfo[],
    user: EmployeeInfo[],
}
// 根据id获取配置详情
export const selectBsDetailById = buildApi<
    { id: number },
    ApiResponse<DetailItem>
>('POST', '/web/assistant/bizConfig/getConfigById', {
    baseUrl: getBaseUrl(),
    transform: (res) => res as ApiResponse<DetailItem>,
})
interface SaveUserBSkillsParams {
    id?: string
    name: string
    url: string
    showUrl: string[]
    superAdmin: EmployeeInfo[]
    admin: EmployeeInfo[]
    user: EmployeeInfo[]
    isShowInBar?: boolean
}
// 保存业务技能配置
export const saveBsConfig = buildApi<
    SaveUserBSkillsParams,
    ApiResponse<any>
>('POST', '/web/assistant/bizConfig/saveConfig', {
    baseUrl: getBaseUrl(),
    transform: (res) => res as ApiResponse<any>,
})