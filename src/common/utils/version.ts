import { useState, useEffect } from 'react'
import { buildApi } from './api'
import { getBaseUrl } from './baseUrl'

export interface ApiResponse<T> {
  code: string
  msg: string
  resultData: T
}

export interface GetLastVersionRequest {
  version: string
}

export interface GetLastVersionResponse {
  isLatestVersion: boolean
  latestVersion: string
  updateType: number // 0: 强制更新, 1: 非强制更新
  description?: string // 版本更新描述（可选）
}

export interface UseVersionReturn {
  isLatestVersion: boolean | null
  latestVersion: string | null
  updateType: number | null // 0: 强制更新, 1: 非强制更新
  description: string | null // 版本更新描述
}

// 跳过版本信息的存储结构
interface SkippedVersionInfo {
  version: string
  skipped: boolean
  timestamp: number
}

/**
 * 获取当前插件版本
 */
export const getCurrentVersion = (): string => {
  return chrome.runtime.getManifest().version
}

/**
 * 对比并获取最新插件版本号
 */
export const getLastVersion = buildApi<
  GetLastVersionRequest,
  ApiResponse<GetLastVersionResponse>
>('POST', '/web/assistant/version/getLastVersion', {
  baseUrl: getBaseUrl(),
  transform: (res) => res as ApiResponse<GetLastVersionResponse>,
})

/**
 * 检查是否应该跳过版本更新提示
 */
const shouldSkipVersionUpdate = (latestVersion: string): boolean => {
  try {
    const skippedVersionStr = localStorage.getItem('skippedVersionInfo')
    if (!skippedVersionStr) return false

    const skippedVersionInfo: SkippedVersionInfo = JSON.parse(skippedVersionStr)

    // 如果记录的是当前最新版本，则跳过更新提示
    if (skippedVersionInfo.skipped && skippedVersionInfo.version === latestVersion) {
      return true
    }

    // 如果版本不同，清除旧的跳过记录
    if (skippedVersionInfo.version !== latestVersion) {
      localStorage.removeItem('skippedVersionInfo')
    }

    return false
  } catch (error) {
    console.error('检查跳过版本时出错:', error)
    return false
  }
}

/**
 * 标记版本为已跳过
 */
const markVersionAsSkipped = (version: string) => {
  try {
    const skippedVersionInfo: SkippedVersionInfo = {
      version,
      skipped: true,
      timestamp: Date.now()
    }
    localStorage.setItem('skippedVersionInfo', JSON.stringify(skippedVersionInfo))
  } catch (error) {
    console.error('标记跳过版本时出错:', error)
  }
}

/**
 * 版本检查 Hook
 * @returns {UseVersionReturn} 版本检查结果
 */
export const useVersion = (): UseVersionReturn => {
  const [isLatestVersion, setIsLatestVersion] = useState<boolean | null>(null)
  const [latestVersion, setLatestVersion] = useState<string | null>(null)
  const [updateType, setUpdateType] = useState<number | null>(null) // 0: 强制更新, 1: 非强制更新
  const [description, setDescription] = useState<string | null>(null) // 版本更新描述

  useEffect(() => {
    const checkVersion = async () => {
      try {
        const currentVersion = getCurrentVersion()
        const result = await getLastVersion({ version: currentVersion }) as ApiResponse<GetLastVersionResponse>
        console.log('getLastVersion', result)
        if (result.code === '0' && result.resultData) {
          const { isLatestVersion, latestVersion, updateType, description } = result.resultData

          // 如果不是强制更新且用户选择跳过该版本，则认为是最新的
          // updateType === 1 表示非强制更新
          const shouldSkip = updateType === 1 && shouldSkipVersionUpdate(latestVersion)

          setIsLatestVersion(isLatestVersion || shouldSkip)
          setLatestVersion(latestVersion)
          setUpdateType(updateType)
          setDescription(description || null)
          console.log('getLastVersion', {
            currentVersion,
            isLatestVersion,
            latestVersion,
            updateType,
            description
          })

        } else {
          setIsLatestVersion(null)
          setLatestVersion(null)
          setUpdateType(null)
          setDescription(null)
        }
      } catch (err) {
        console.error('版本检查失败:', err)
        setIsLatestVersion(null)
        setLatestVersion(null)
        setUpdateType(null)
        setDescription(null)
      }
    }

    checkVersion()
  }, [])

  return {
    isLatestVersion,
    latestVersion,
    updateType,
    description
  }
}

export default useVersion