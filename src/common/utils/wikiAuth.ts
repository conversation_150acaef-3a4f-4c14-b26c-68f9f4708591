/**
 * Wiki认证相关工具
 * 直接从浏览器cookie中获取和验证wiki认证信息，不进行本地存储
 */

import axios from 'axios'

/**
 * Wiki配置
 */
export const WIKI_CONFIG = {
    BASE_URL: 'http://wiki.htzq.htsc.com.cn',
    DOMAIN: '.htzq.htsc.com.cn',
    LOGIN_MONITOR_URL: 'http://wiki.htzq.htsc.com.cn/dologin.action',
    VALIDATION_URL: 'http://wiki.htzq.htsc.com.cn/',
    LOGIN_PAGE_URL: 'http://wiki.htzq.htsc.com.cn'
}

/**
 * 从浏览器获取Wiki域名的所有cookie
 * @returns Promise<string> 返回cookie字符串，如果没有cookie返回空字符串
 */
export const getWikiCookie = async (): Promise<string> => {
    return new Promise((resolve, reject) => {
        chrome.cookies.getAll({
            domain: WIKI_CONFIG.DOMAIN
        }, (cookies) => {
            if (chrome.runtime.lastError) {
                console.error('获取Wiki cookie失败:', chrome.runtime.lastError)
                reject(chrome.runtime.lastError)
                return
            }

            // 将所有cookie组合成字符串
            const cookieString = cookies
                .map(cookie => `${cookie.name}=${cookie.value}`)
                .join('; ')

            console.log('获取Wiki cookies:', cookieString || '(无cookie)')
            resolve(cookieString)
        })
    })
}

/**
 * 验证当前浏览器中的Wiki cookie是否有效（后台验证，使用axios）
 * @returns Promise<boolean> cookie是否有效
 */
export const validateWikiCookie = async (): Promise<boolean> => {
    try {
        console.log('开始验证Wiki cookie...')

        // 获取当前浏览器中的cookie
        const cookieString = await getWikiCookie()
        if (!cookieString) {
            console.log('没有找到Wiki cookie')
            return false
        }

        // 使用axios进行后台验证，让浏览器自动携带cookie
        try {
            const response = await axios.get(WIKI_CONFIG.VALIDATION_URL, {
                withCredentials: true,  // 让浏览器自动携带cookie
                timeout: 10000,
                validateStatus: (status) => {
                    // 允许所有状态码，我们手动判断
                    return true
                }
            })

            console.log('Wiki cookie验证响应状态码:', response.status)

            if (response.status === 200) {
                console.log('Wiki cookie验证成功（后台验证）')
                return true
            } else {
                console.log('Wiki cookie已失效，状态码:', response.status)
                return false
            }
        } catch (error) {
            console.error('Wiki cookie验证请求失败:', error)
            return false
        }
    } catch (error) {
        console.error('验证Wiki cookie时发生错误:', error)
        return false
    }
}

/**
 * 检查Wiki认证状态
 * @returns Promise<boolean> 是否有有效的wiki认证信息
 */
export const checkWikiAuthStatus = async (): Promise<boolean> => {
    try {
        // 直接验证当前浏览器中的cookie
        return await validateWikiCookie()
    } catch (error) {
        console.error('检查Wiki认证状态失败:', error)
        return false
    }
}

/**
 * 打开Wiki登录页面
 * @returns Promise<number> 返回打开的tab ID
 */
export const openWikiLoginPage = async (): Promise<number> => {
    // 获取当前活跃的tab ID并保存
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
    const currentTab = tabs[0]

    if (currentTab && currentTab.id) {
        // 保存当前tab ID到storage，供登录成功后使用
        await chrome.storage.session.set({ 'originalTabId': currentTab.id })
        console.log('保存原始tab ID:', currentTab.id)
    }

    return new Promise((resolve, reject) => {
        chrome.tabs.create({
            url: WIKI_CONFIG.LOGIN_PAGE_URL,
            active: true
        }, (tab) => {
            if (chrome.runtime.lastError) {
                console.error('打开Wiki登录页面失败:', chrome.runtime.lastError)
                reject(chrome.runtime.lastError)
                return
            }

            if (tab && tab.id) {
                console.log('已打开Wiki登录页面，tab ID:', tab.id)
                resolve(tab.id)
            } else {
                reject(new Error('无法获取tab ID'))
            }
        })
    })
}

/**
 * 监控Wiki登录状态
 * @param tabId 登录页面的tab ID
 * @returns Promise<boolean> 是否登录成功
 */
export const monitorWikiLogin = async (tabId: number): Promise<boolean> => {
    return new Promise((resolve) => {
        let timeoutId: NodeJS.Timeout
        let isResolved = false

        const cleanupAndResolve = (result: boolean) => {
            if (isResolved) return
            isResolved = true

            chrome.runtime.onMessage.removeListener(messageListener)
            // chrome.tabs.onUpdated.removeListener(tabUpdateListener)
            // chrome.tabs.onRemoved.removeListener(tabRemovedListener)
            clearTimeout(timeoutId)

            resolve(result)
        }

        const handleLoginSuccess = async () => {
            try {
                // 检查是否有有效的cookie
                const cookieString = await getWikiCookie()
                if (cookieString) {
                    console.log('检测到Wiki登录成功，获取到cookie:', cookieString)

                    // 关闭登录页面
                    chrome.tabs.remove(tabId, () => {
                        console.log('已关闭Wiki登录页面')
                    })

                    // 跳转回原始tab
                    try {
                        const result = await chrome.storage.session.get('originalTabId')
                        if (result.originalTabId) {
                            console.log('跳转回原始tab:', result.originalTabId)
                            await chrome.tabs.update(result.originalTabId, { active: true })
                            // 清除保存的原始tab ID
                            await chrome.storage.session.remove('originalTabId')
                        }
                    } catch (error) {
                        console.warn('跳转回原始tab失败:', error)
                    }

                    cleanupAndResolve(true)
                } else {
                    console.warn('登录后未获取到cookie，稍后重试...')
                    // 稍等一下再试，可能cookie还没设置完成
                    setTimeout(async () => {
                        if (isResolved) return

                        const retryString = await getWikiCookie()
                        if (retryString) {
                            console.log('重试获取Wiki cookie成功:', retryString)
                            chrome.tabs.remove(tabId, () => {
                                console.log('已关闭Wiki登录页面')
                            })

                            // 跳转回原始tab
                            try {
                                const result = await chrome.storage.session.get('originalTabId')
                                if (result.originalTabId) {
                                    console.log('跳转回原始tab:', result.originalTabId)
                                    await chrome.tabs.update(result.originalTabId, { active: true })
                                    // 清除保存的原始tab ID
                                    await chrome.storage.session.remove('originalTabId')
                                }
                            } catch (error) {
                                console.warn('跳转回原始tab失败:', error)
                            }

                            cleanupAndResolve(true)
                        } else {
                            console.warn('重试后仍未获取到cookie，继续监控...')
                        }
                    }, 2000)
                }
            } catch (error) {
                console.error('处理登录成功时发生错误:', error)
            }
        }

        // 监听来自background script的消息
        const messageListener = (message: any, sender: chrome.runtime.MessageSender, sendResponse: (response?: any) => void) => {
            if (isResolved) return

            if (message.type === 'wikiLoginSuccess' && message.payload?.tabId === tabId) {
                console.log('收到Wiki登录成功消息 (302状态码):', message.payload)
                // 稍等一下让cookie设置完成，然后处理登录成功
                setTimeout(handleLoginSuccess, 500)
            } else if (message.type === 'wikiLoginFailed' && message.payload?.tabId === tabId) {
                console.log('收到Wiki登录失败消息:', message.payload)
                // 可以在这里处理登录失败的情况，但不立即结束监控，让用户可以重试
            }
        }

        // // 监听tab更新事件（作为备用方案）
        // const tabUpdateListener = (updatedTabId: number, changeInfo: chrome.tabs.TabChangeInfo, tab: chrome.tabs.Tab) => {
        //     if (isResolved) return

        //     if (updatedTabId === tabId && changeInfo.url) {
        //         console.log('Wiki登录页面URL变化:', changeInfo.url)

        //         // 检测登录页面变化
        //         if (changeInfo.url.includes('login.action')) {
        //             console.log('用户在Wiki登录页面')
        //         }
        //         // 如果跳转到成功页面，可能是登录成功的备用检测
        //         else if (changeInfo.url.includes('index.action') &&
        //             !changeInfo.url.includes('login.action')) {
        //             console.log('检测到页面跳转到首页，可能登录成功（备用检测）')
        //             setTimeout(handleLoginSuccess, 1000)
        //         }
        //     }
        // }

        // // 监听tab关闭事件
        // const tabRemovedListener = (removedTabId: number) => {
        //     if (isResolved) return

        //     if (removedTabId === tabId) {
        //         console.log('Wiki登录页面被用户关闭')
        //         cleanupAndResolve(false)
        //     }
        // }

        // 添加所有监听器
        chrome.runtime.onMessage.addListener(messageListener)
        // chrome.tabs.onUpdated.addListener(tabUpdateListener)
        // chrome.tabs.onRemoved.addListener(tabRemovedListener)

        // 设置超时，避免无限等待
        timeoutId = setTimeout(() => {
            console.log('Wiki登录监控超时 (5分钟)')
            cleanupAndResolve(false)
        }, 300000) // 5分钟超时

        console.log(`开始监控Wiki登录状态，tab ID: ${tabId}`)
    })
}

/**
 * 触发Wiki登录流程
 * @returns Promise<boolean> 是否登录成功
 */
export const triggerWikiLogin = async (): Promise<boolean> => {
    try {
        console.log('开始Wiki登录流程...')

        // 打开登录页面
        const tabId = await openWikiLoginPage()

        // 监控登录状态
        const loginSuccess = await monitorWikiLogin(tabId)

        if (loginSuccess) {
            console.log('Wiki登录流程完成，认证成功')
            return true
        } else {
            console.log('Wiki登录流程完成，认证失败或被取消')
            return false
        }
    } catch (error) {
        console.error('Wiki登录流程失败:', error)
        return false
    }
}

/**
 * 获取Wiki认证信息（从浏览器cookie）
 * @returns Promise<{cookie: string} | null> 返回有效的认证信息或null
 */
export const getWikiAuth = async (): Promise<{ cookie: string } | null> => {
    try {
        console.log('获取Wiki认证信息...')

        const cookieString = await getWikiCookie()
        if (cookieString) {
            return { cookie: cookieString }
        } else {
            console.log('没有找到Wiki认证信息')
            return null
        }
    } catch (error) {
        console.error('获取Wiki认证信息失败:', error)
        return null
    }
}

/**
 * 获取有效的Wiki认证信息（带验证）
 * @returns Promise<{cookie: string} | null> 返回有效的认证信息或null
 */
export const getValidWikiAuth = async (): Promise<{ cookie: string } | null> => {
    try {
        console.log('获取有效的Wiki认证信息...')

        // 先检查是否有有效的认证
        const isValid = await checkWikiAuthStatus()

        if (isValid) {
            const cookieString = await getWikiCookie()
            if (cookieString) {
                console.log('获取到有效的Wiki认证信息')
                return { cookie: cookieString }
            }
        } else {
            console.log('当前没有有效的Wiki认证信息')
        }

        return null
    } catch (error) {
        console.error('获取Wiki认证信息失败:', error)
        return null
    }
}

/**
 * EIP登录成功后调用的Wiki认证检查（静默模式）
 * 只检查当前状态，不自动触发登录
 */
export const checkWikiAuthSilent = async (): Promise<void> => {
    try {
        console.log('静默检查Wiki认证状态...')

        const wikiAuth = await getValidWikiAuth()

        if (wikiAuth) {
            console.log('Wiki认证信息获取成功:', {
                authInfo: {
                    wiki: wikiAuth
                }
            })
        } else {
            console.log('当前没有有效的Wiki认证信息')
            console.log('用户可以手动调用 triggerWikiLogin() 进行Wiki登录')
        }
    } catch (error) {
        console.error('检查Wiki认证信息失败:', error)
    }
}

/**
 * EIP登录成功后调用的Wiki认证检查（自动登录模式）
 * 检查当前状态，如果没有有效认证则自动触发登录
 */
export const fetchAndStoreWikiAuth = async (): Promise<void> => {
    try {
        console.log('检查Wiki认证状态（自动登录模式）...')

        const wikiAuth = await getValidWikiAuth()

        if (wikiAuth) {
            console.log('Wiki认证信息获取成功:', {
                authInfo: {
                    wiki: wikiAuth
                }
            })
        } else {
            console.log('当前没有有效的Wiki认证信息，自动触发Wiki登录...')

            try {
                const loginSuccess = await triggerWikiLogin()

                if (loginSuccess) {
                    // 登录成功后重新获取认证信息
                    const newWikiAuth = await getWikiAuth()
                    if (newWikiAuth) {
                        console.log('Wiki自动登录成功，认证信息获取成功:', {
                            authInfo: {
                                wiki: newWikiAuth
                            }
                        })
                    }
                } else {
                    console.log('Wiki自动登录失败或被用户取消')
                }
            } catch (loginError) {
                console.error('Wiki自动登录过程中发生错误:', loginError)
            }
        }
    } catch (error) {
        console.error('检查Wiki认证信息失败:', error)
        // 不抛出错误，避免影响EIP登录流程
    }
}

/**
 * 确保获取有效的Wiki认证信息（带自动登录）
 * @returns Promise<{cookie: string} | null> 返回有效的认证信息或null
 */
export const ensureWikiAuth = async (): Promise<{ cookie: string } | null> => {
    try {
        console.log('确保获取有效的Wiki认证信息...')

        // 先检查当前状态
        let wikiAuth = await getValidWikiAuth()

        if (wikiAuth) {
            console.log('已有有效的Wiki认证信息')
            return wikiAuth
        }

        console.log('没有有效的Wiki认证信息，触发登录...')

        // 触发登录
        const loginSuccess = await triggerWikiLogin()

        if (loginSuccess) {
            // 登录成功后重新获取
            wikiAuth = await getWikiAuth()
            if (wikiAuth) {
                console.log('Wiki登录成功，获取到认证信息')
                return wikiAuth
            }
        }

        console.log('无法获取有效的Wiki认证信息')
        return null
    } catch (error) {
        console.error('确保Wiki认证时发生错误:', error)
        return null
    }
}