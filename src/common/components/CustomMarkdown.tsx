import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface CustomMarkdownProps {
    children: string;
    searchKeyword?: string;
    className?: string;
}

const CustomMarkdown: React.FC<CustomMarkdownProps> = ({
    children,
    searchKeyword = '',
    className = 'markdown-body'
}) => {
    // 自定义链接组件，处理特殊链接协议
    const LinkComponent = ({ href, children }: any) => {
        // 检查是否为特殊协议链接
        if (href && (href.startsWith('http://action:') || href.startsWith('https://action:'))) {
            // 对于特殊链接，只显示文本，不添加链接行为
            return <span>{children}</span>;
        }
        // 其他链接正常渲染
        return <a href={href} target="_blank" rel="noopener noreferrer">{children}</a>;
    };

    // 高亮组件
    const HighlightComponent = ({ children }: { children: React.ReactNode }) => {
        if (!searchKeyword.trim()) {
            return <span>{children}</span>;
        }

        const highlightText = (text: string, keyword: string) => {
            if (!text || !keyword.trim()) return text;

            try {
                const parts = text.split(new RegExp(`(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi'));
                return (
                    <>
                        {parts.map((part, index) =>
                            part.toLowerCase() === keyword.toLowerCase() ? (
                                <mark key={index} style={{ backgroundColor: '#ffecb3', fontWeight: 'bold' }}>{part}</mark>
                            ) : (
                                part
                            )
                        )}
                    </>
                );
            } catch (e) {
                return text;
            }
        };

        // 如果children是字符串，直接高亮
        if (typeof children === 'string') {
            return <>{highlightText(children, searchKeyword)}</>;
        }

        // 如果只有一个子元素且是文本节点，进行高亮
        if (React.Children.count(children) === 1) {
            const child = React.Children.toArray(children)[0];
            if (typeof child === 'string') {
                return <>{highlightText(child, searchKeyword)}</>;
            }
        }

        // 其他情况直接返回
        return <>{children}</>;
    };

    // 预处理文本，将特殊协议链接转换为纯文本
    const processedChildren = children ? children.replace(/\[([^\]]+)\]\(http:\/\/action:(\d+)([^\)]*)\)/g, '$1') : '';

    if (!searchKeyword.trim()) {
        return (
            <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                className={className}
                components={{
                    a: LinkComponent
                }}
            >
                {processedChildren}
            </ReactMarkdown>
        );
    }

    // 自定义渲染组件，用于在文本中添加高亮
    const customComponents = {
        a: LinkComponent,
        p: ({ children, ...props }: any) => (
            <p {...props}>
                <HighlightComponent>
                    {children}
                </HighlightComponent>
            </p>
        ),
        span: ({ children, ...props }: any) => (
            <span {...props}>
                <HighlightComponent>
                    {children}
                </HighlightComponent>
            </span>
        ),
        text: ({ children }: any) => (
            <HighlightComponent>
                {children}
            </HighlightComponent>
        ),
        li: ({ children, ...props }: any) => (
            <li {...props}>
                <HighlightComponent>
                    {children}
                </HighlightComponent>
            </li>
        ),
        td: ({ children, ...props }: any) => (
            <td {...props}>
                <HighlightComponent>
                    {children}
                </HighlightComponent>
            </td>
        ),
        th: ({ children, ...props }: any) => (
            <th {...props}>
                <HighlightComponent>
                    {children}
                </HighlightComponent>
            </th>
        )
    };

    return (
        <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            className={className}
            components={customComponents}
        >
            {processedChildren}
        </ReactMarkdown>
    );
};

export default CustomMarkdown;