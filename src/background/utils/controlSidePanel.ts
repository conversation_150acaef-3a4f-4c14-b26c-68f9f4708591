export type TMessageSender = chrome.runtime.MessageSender

/**
 * 侧边栏的切换逻辑
 */
export default (() => {
  // 使用Map来跟踪每个窗口的侧边栏状态
  const sidePanelStates = new Map<number, boolean>()

  // 使用Map来跟踪每个窗口正在进行的操作，防止竞态条件
  const pendingOperations = new Map<number, Promise<void>>()

  // 监听窗口关闭事件，清理状态
  chrome.windows.onRemoved.addListener((windowId) => {
    if (sidePanelStates.has(windowId)) {
      console.log(`[SidePanel] 清理已关闭窗口 ${windowId} 的状态`)
      sidePanelStates.delete(windowId)
      pendingOperations.delete(windowId)
    }
  })

  // 1. 点击插件图标的事件监听器
  chrome.action.onClicked.addListener(async (tab) => {
    if (!tab.id) return

    await toggleSidePanel({
      tab: { id: tab.id, windowId: tab.windowId },
    } as TMessageSender)
  })

  // 获取指定窗口的侧边栏状态
  function getSidePanelState(windowId: number): boolean {
    return sidePanelStates.get(windowId) || false
  }

  // 设置指定窗口的侧边栏状态
  function setSidePanelState(windowId: number, isOpen: boolean) {
    sidePanelStates.set(windowId, isOpen)
  }

  // 确保操作串行执行，防止竞态条件
  async function executeWithLock(windowId: number, operation: () => Promise<void>): Promise<void> {
    // 等待之前的操作完成
    const existingOperation = pendingOperations.get(windowId)
    if (existingOperation) {
      console.log(`[SidePanel] 等待窗口 ${windowId} 的前一个操作完成`)
      try {
        await existingOperation
      } catch (error) {
        // 忽略前一个操作的错误，继续执行新操作
        console.log(`[SidePanel] 前一个操作出错，继续执行新操作:`, error)
      }
    }

    // 执行新操作
    const newOperation = operation()
    pendingOperations.set(windowId, newOperation)

    try {
      await newOperation
    } finally {
      // 操作完成后清理
      if (pendingOperations.get(windowId) === newOperation) {
        pendingOperations.delete(windowId)
      }
    }
  }

  // 2. 切换侧边栏的函数
  async function toggleSidePanel(sender: TMessageSender) {
    const windowId = sender.tab?.windowId

    if (!windowId) {
      return
    }

    await executeWithLock(windowId, async () => {
      const currentState = getSidePanelState(windowId)

      if (currentState) {
        // 如果侧边栏已经打开，关闭它
        console.log(`[SidePanel] 切换：关闭窗口 ${windowId} 的侧边栏`)
        await closeSidePanelInternal(windowId)
      } else {
        // 如果侧边栏关闭，则打开它
        console.log(`[SidePanel] 切换：打开窗口 ${windowId} 的侧边栏`)
        await openSidePanelInternal(windowId)
      }
    })
  }

  // 关闭侧边栏的内部函数（不使用锁，由调用者控制）
  async function closeSidePanelInternal(windowId: number) {
    try {
      // 先禁用侧边栏
      await chrome.sidePanel.setOptions({
        enabled: false,
      })

      // 更新状态
      setSidePanelState(windowId, false)

      // 缩短等待时间，减少竞态条件窗口
      await new Promise(resolve => setTimeout(resolve, 100))

      // 重新启用侧边栏，为下次打开做准备
      await chrome.sidePanel.setOptions({
        enabled: true,
      })

      console.log(`[SidePanel] 窗口 ${windowId} 的侧边栏已关闭`)
    } catch (error) {
      console.error(`关闭侧边栏失败 (窗口 ${windowId}):`, error)
      // 发生错误时重置状态
      setSidePanelState(windowId, false)
      throw error
    }
  }

  // 打开侧边栏的内部函数（不使用锁，由调用者控制）
  async function openSidePanelInternal(windowId: number) {
    try {
      await chrome.sidePanel.open({ windowId })
      setSidePanelState(windowId, true)
      console.log(`[SidePanel] 窗口 ${windowId} 的侧边栏已打开`)
    } catch (error) {
      console.error(`打开侧边栏失败 (窗口 ${windowId}):`, error)
      // 发生错误时重置状态
      setSidePanelState(windowId, false)
      throw error
    }
  }

  async function openSidePanel(sender: TMessageSender) {
    const windowId = sender.tab?.windowId

    if (!windowId) {
      return
    }

    await executeWithLock(windowId, async () => {
      console.log(`[SidePanel] 强制打开窗口 ${windowId} 的侧边栏`)
      await openSidePanelInternal(windowId)
    })
  }

  // 获取侧边栏状态的函数（兼容旧接口）
  function isSidePanelOpen(windowId?: number): boolean {
    if (windowId) {
      return getSidePanelState(windowId)
    }
    // 如果没有指定窗口ID，返回是否有任何窗口的侧边栏是打开的
    return Array.from(sidePanelStates.values()).some(state => state)
  }

  return {
    toggleSidePanel,
    openSidePanel,
    isSidePanelOpen,
    getSidePanelState,
    setSidePanelState,
  }
})()