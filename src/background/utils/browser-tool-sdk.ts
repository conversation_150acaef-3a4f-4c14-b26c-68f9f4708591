/**
 * Browser Tool SDK
 * 基于Chrome Extension的浏览器自动化工具SDK
 *
 * 使用方法:
 * const sdk = new BrowserToolSDK({
 *   serverUrl: 'ws://localhost:8765',
 *   autoConnect: true
 * });
 *
 * 主要功能:
 * - 页面信息获取
 * - 用户操作模拟
 * - 代码执行能力
 * - 标签页管理
 * - WebSocket通信
 */

import { convertHtmlToLlmMarkdown } from '@src/sidepanel/utils/html2md'
import { Readability } from '@mozilla/readability'

// Chrome Extension类型声明
interface ChromeTab extends chrome.tabs.Tab {
  lastAccessed?: number;
}

// CDP相关类型
interface CDPResult {
  [key: string]: any;
}

// 类型定义
export interface BrowserToolSDKOptions {
  serverUrl?: string;
  autoConnect?: boolean;
  autoReconnect?: boolean;
  reconnectDelay?: number;
  enableLogging?: boolean;
  enableCDP?: boolean;
  clientId?: string;
}

export interface PageInfo {
  title: string;
  url: string;
  domain: string;
  timestamp: string;
  viewport: {
    width: number;
    height: number;
  };
  scrollPosition: {
    x: number;
    y: number;
  };
  text?: string;
  dom?: string;
  domLength?: number;
}

export interface ClickElementParams {
  selector?: string;
  coordinates?: {
    x: number;
    y: number;
  };
}

export interface UserActionParams {
  target?: {
    selector?: string;
    coordinates?: { x: number; y: number };
    url?: string;
    tab_id?: number;
  };
  params?: {
    value?: string;
    direction?: 'up' | 'down' | 'left' | 'right';
    amount?: number;
    form_data?: Record<string, string>;
  };
}

export interface CodeActionParams {
  params?: {
    format?: 'png' | 'jpeg';
    quality?: number;
    code?: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    url?: string;
    headers?: Record<string, string>;
    data?: any;
    timeout?: number;
  };
}

export interface TabInfo {
  id: number;
  title: string;
  url: string;
  active: boolean;
  windowId: number;
}

export interface ActionResult {
  success: boolean;
  [key: string]: any;
}

export interface ConnectionStatus {
  connected: boolean;
  clientId: string;
  serverUrl: string;
}

export interface WebSocketMessage {
  id?: string;
  type: 'register' | 'request' | 'response' | 'page_changed' | 'error';
  action?: string;
  params?: any;
  success?: boolean;
  result?: any;
  error?: string;
  clientId?: string;
  clientType?: string;
  capabilities?: string[];
  targetTabId?: number;
  wsSessionId?: string;
}

export class BrowserToolSDK {
  private serverUrl: string;
  private clientId: string;
  private ws: WebSocket | null = null;
  private isConnected: boolean = false;
  private registeredTabs: Map<number, any> = new Map();
  private callbacks: Map<string, Function> = new Map();
  private messageId: number = 0;

  // 配置选项
  private autoReconnect: boolean;
  private reconnectDelay: number;
  private enableLogging: boolean;
  private enableCDP: boolean;

  constructor(options: BrowserToolSDKOptions = {}) {
    this.serverUrl = options.serverUrl || 'ws://localhost:8765';
    // 使用传入的clientId，如果没有则生成一个
    this.clientId = options.clientId || this.generateClientId();

    // 配置选项
    this.autoReconnect = options.autoReconnect !== false;
    this.reconnectDelay = options.reconnectDelay || 3000;
    this.enableLogging = options.enableLogging !== false;
    this.enableCDP = options.enableCDP === true;

    if (options.autoConnect !== false) {
      this.init();
    }
  }

  private generateClientId(): string {
    return 'browser_tool_sdk_' + Math.random().toString(36).substr(2, 9);
  }

  private log(message: string, ...args: any[]): void {
    if (this.enableLogging) {
      console.log(`[BrowserToolSDK] ${message}`, ...args);
    }
  }

  private async init(): Promise<void> {
    this.log('🚀 初始化Browser Tool SDK');
    await this.connect();
    this.setupMessageHandlers();
  }

  // === WebSocket连接管理 ===

  async connect(): Promise<void> {
    if (this.isConnected) {
      this.log('已经连接到服务器');
      return;
    }

    try {
      this.log(`🔌 连接到 ${this.serverUrl}`);
      this.ws = new WebSocket(this.serverUrl);

      this.ws.onopen = () => {
        this.log('🔗 WebSocket连接成功');
        this.isConnected = true;
        this.register();
      };

      this.ws.onmessage = (event) => {
        this.handleServerMessage(JSON.parse(event.data));
      };

      this.ws.onclose = () => {
        this.log('❌ 连接断开');
        this.isConnected = false;
        if (this.autoReconnect) {
          this.log(`🔄 ${this.reconnectDelay / 1000}秒后重连...`);
          setTimeout(() => this.connect(), this.reconnectDelay);
        }
      };

      this.ws.onerror = (error) => {
        this.log('WebSocket连接失败:', error);
      };

    } catch (error) {
      this.log('连接失败:', error);
      throw error;
    }
  }

  private register(): void {
    this.send({
      type: 'register',
      clientId: this.clientId,
      clientType: 'browser_tool_sdk',
      capabilities: [
        'get_page_info',
        'simulate_user_action',
        'execute_code',
        'manage_tabs',
        'cdp_real_hardware_events',
        'unlimited_dom_access'
      ]
    });
  }

  private send(message: WebSocketMessage): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      this.log('WebSocket未连接，无法发送消息:', message);
    }
  }

  private async handleServerMessage(message: WebSocketMessage): Promise<void> {
    const { id, type, action, params, targetTabId, wsSessionId } = message;

    try {
      if (type === 'register' && message.success) {
        this.log('✅ 注册成功');
        return;
      }

      if (type === 'request' && action) {
        this.log(`🔧 服务器请求操作: ${action}`);

        // 转换MCP工具名到内部方法名
        const internalAction = action.startsWith('browser_') ? action.replace('browser_', '') : action;
        const result = await this.handleActionRequest(internalAction, params, targetTabId);

        this.send({
          id,
          type: 'response',
          success: true,
          result: result,
          wsSessionId: wsSessionId
        });

        this.log(`✅ ${action} 执行完成`);
      }

    } catch (error) {
      this.log('处理服务器消息失败:', error);
      this.send({
        id,
        type: 'response',
        success: false,
        error: (error as Error).message,
        action: action,
        wsSessionId: wsSessionId
      });
    }
  }

  private setupMessageHandlers(): void {
    // 监听Chrome扩展消息（如果需要）
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        this.log('收到Chrome扩展消息:', request);
        // 处理扩展内部消息
        return true;
      });
    }
  }

  // === 核心操作处理 ===

  private async handleActionRequest(action: string, params: any, targetTabId?: number): Promise<any> {
    let tabId = targetTabId;

    // 智能标签页ID确定
    if (!tabId) {
      tabId = await this.determineTabId();
    }

    if (!tabId) {
      throw new Error('无法确定目标标签页');
    }

    switch (action) {
      case 'get_page_info':
        return await this.getPageInfo(tabId, params);

      case 'simulate_user_action':
        return await this.executeUserAction(tabId, params.action_type, params);

      case 'execute_code':
        return await this.executeCodeAction(tabId, params.code_type, params);

      case 'get_all_tabs':
        return await this.getAllTabs();

      case 'switch_tab':
        return await this.switchTab(params.tabId);

      default:
        throw new Error(`不支持的操作: ${action}`);
    }
  }

  private async determineTabId(): Promise<number | null> {
    if (typeof chrome === 'undefined' || !chrome.tabs) {
      throw new Error('Chrome tabs API不可用');
    }

    try {
      // 方法1：获取当前活动标签页
      const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (activeTab && activeTab.id) {
        this.log(`📄 使用活动标签页: ${activeTab.id} (${activeTab.title})`);
        return activeTab.id;
      }

      // 方法2：使用最近的有效标签页
      const allTabs = await chrome.tabs.query({}) as ChromeTab[];
      const validTabs = allTabs.filter(tab =>
        tab.id &&
        !tab.url?.startsWith('chrome://') &&
        !tab.url?.startsWith('chrome-extension://')
      );

      if (validTabs.length > 0) {
        validTabs.sort((a, b) => (b.lastAccessed || 0) - (a.lastAccessed || 0));
        if (validTabs[0].id) {
          this.log(`📄 使用最近标签页: ${validTabs[0].id} (${validTabs[0].title})`);
          return validTabs[0].id;
        }
      }

      return null;
    } catch (error) {
      this.log('确定标签页ID失败:', error);
      return null;
    }
  }

  // === 公共API方法 ===

  /**
   * 使用 Readability 提取正文，失败则回退到整页转换
   */
  private convertWithReadability(html: string, pageUrl: string): string {
    const htmlContent = convertHtmlToLlmMarkdown(html)
    try {
      const parser = new DOMParser()
      const doc = parser.parseFromString(html || '', 'text/html')
      // 为相对链接提供 base
      if (doc?.head) {
        const base = doc.createElement('base')
        base.href = pageUrl || document.baseURI || ''
        doc.head.appendChild(base)
      }

      const cloned = doc.cloneNode(true) as Document
      const article = new Readability(cloned).parse()
      if (article?.content) {
        const articleContent = convertHtmlToLlmMarkdown(article.content)
        return articleContent
      }
      return htmlContent
    } catch (e) {
      return htmlContent
    }
  }

  /**
   * 获取页面信息
   */
  async getPageInfo(tabId: number, params: {
    include_dom?: boolean;
    include_text?: boolean;
    max_text_length?: number;
    max_dom_length?: number;
  } = {}): Promise<PageInfo> {
    const {
      include_dom = true,
      include_text = true,
      max_text_length = Number.MAX_SAFE_INTEGER,
      max_dom_length = 50000
    } = params;

    if (typeof chrome === 'undefined' || !chrome.scripting) {
      throw new Error('Chrome scripting API不可用');
    }

    // 首先获取完整的HTML内容
    const htmlResults = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => '<!DOCTYPE html>\n' + document.documentElement.outerHTML
    });

    const htmlContent = htmlResults[0].result || '';

    // 获取页面基本信息
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      func: (opts: {
        include_dom: boolean;
        include_text: boolean;
        max_text_length: number;
        max_dom_length: number;
      }) => {
        const pageData: any = {
          title: document.title,
          url: location.href,
          domain: location.hostname,
          timestamp: new Date().toISOString(),
          viewport: {
            width: window.innerWidth,
            height: window.innerHeight
          },
          scrollPosition: {
            x: window.scrollX,
            y: window.scrollY
          }
        };

        return pageData;
      },
      args: [{ include_dom, include_text, max_text_length, max_dom_length }],
      world: 'MAIN'
    });

    const pageData = results[0].result;

    // 使用 Readability 处理页面文本内容
    if (include_text && htmlContent) {
      let markdownContent = this.convertWithReadability(htmlContent, pageData.url || '');
      if (max_text_length && markdownContent.length > max_text_length) {
        markdownContent = markdownContent.substring(0, max_text_length) + '...';
      }
      pageData.text = markdownContent;
    }

    // DOM结构处理
    if (include_dom && htmlContent) {
      const domResults = await chrome.scripting.executeScript({
        target: { tabId },
        func: (opts: { max_dom_length: number }) => {
          const bodyClone = document.body.cloneNode(true) as HTMLElement;

          // 清理DOM
          bodyClone.querySelectorAll('script, style, noscript').forEach(el => el.remove());
          bodyClone.querySelectorAll('*').forEach(el => {
            el.removeAttribute('style');
            Array.from(el.attributes).forEach(attr => {
              if (attr.name.startsWith('on')) {
                el.removeAttribute(attr.name);
              }
            });
          });

          let domHTML = bodyClone.innerHTML;
          if (opts.max_dom_length && domHTML.length > opts.max_dom_length) {
            domHTML = domHTML.substring(0, opts.max_dom_length) + '...[DOM截断]';
          }

          return {
            dom: domHTML,
            domLength: domHTML.length
          };
        },
        args: [{ max_dom_length }],
        world: 'MAIN'
      });

      const domData = domResults[0].result;
      pageData.dom = domData.dom;
      pageData.domLength = domData.domLength;
    }

    return pageData;
  }

  /**
   * 模拟用户操作
   */
  async executeUserAction(
    tabId: number,
    actionType: string,
    params: UserActionParams = {}
  ): Promise<ActionResult> {
    this.log(`👆 模拟用户操作: ${actionType}`, params);

    const target = params.target || {};
    const actionParams = params.params || {};

    switch (actionType) {
      case 'click':
        return await this.clickElement(tabId, {
          selector: target.selector,
          coordinates: target.coordinates
        });

      case 'input':
        return await this.simulateUserInput(tabId, {
          actions: [{
            type: 'input',
            selector: target.selector!,
            value: actionParams.value!
          }]
        });

      case 'scroll':
        return await this.scrollPage(tabId, {
          direction: actionParams.direction || 'down',
          amount: actionParams.amount || 500
        });

      case 'form_fill':
        if (actionParams.form_data) {
          const actions = Object.entries(actionParams.form_data).map(([selector, value]) => ({
            type: 'input',
            selector: selector,
            value: value
          }));
          return await this.simulateUserInput(tabId, { actions });
        } else {
          return await this.simulateUserInput(tabId, params as any);
        }

      case 'navigate':
        return await this.navigatePage(tabId, target.url!);

      case 'open_tab':
        return await this.openTab(target.url!);

      case 'close_tab':
        return await this.closeTab(target.tab_id || tabId);

      case 'switch_tab':
        return await this.switchTab(target.tab_id!);

      default:
        throw new Error(`不支持的用户操作类型: ${actionType}`);
    }
  }

  /**
   * 执行代码操作
   */
  async executeCodeAction(
    tabId: number,
    codeType: string,
    params: CodeActionParams = {}
  ): Promise<ActionResult> {
    this.log(`💻 执行代码操作: ${codeType}`, params);

    const codeParams = params.params || {};

    switch (codeType) {
      case 'screenshot':
        return await this.takeScreenshot(tabId, {
          format: codeParams.format || 'png',
          quality: codeParams.quality || 90
        });

      case 'execute_script':
        return await this.executeScript(tabId, codeParams.code!);

      case 'send_request':
        return await this.sendHttpRequest(codeParams);

      default:
        throw new Error(`不支持的代码执行类型: ${codeType}`);
    }
  }

  // === 具体操作实现 ===

  private async clickElement(tabId: number, params: ClickElementParams): Promise<ActionResult> {
    const { selector, coordinates } = params;

    // 如果启用CDP，使用真实硬件事件
    if (this.enableCDP && selector) {
      return await this.cdpRealClick(tabId, selector, coordinates);
    }

    // 普通JavaScript点击
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      func: (clickParams: ClickElementParams) => {
        const { selector, coordinates } = clickParams;

        if (coordinates) {
          // 按坐标点击
          const element = document.elementFromPoint(coordinates.x, coordinates.y);
          if (element) {
            (element as HTMLElement).click();
            return { success: true, method: 'coordinates' };
          }
        } else if (selector) {
          // 按选择器点击
          const element = document.querySelector(selector);
          if (element) {
            (element as HTMLElement).click();
            return { success: true, method: 'selector' };
          }
        }

        throw new Error('元素未找到或不可点击');
      },
      args: [{ selector, coordinates }],
      world: 'MAIN'
    });

    return results[0].result;
  }

  private async simulateUserInput(tabId: number, params: {
    actions: Array<{
      type: string;
      selector: string;
      value?: string;
    }>;
  }): Promise<ActionResult> {
    const { actions } = params;

    // 如果启用CDP，使用真实硬件事件
    if (this.enableCDP) {
      return await this.cdpSimulateUserInput(tabId, actions);
    }

    // 普通JavaScript操作
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      func: (inputParams: { actions: Array<{ type: string; selector: string; value?: string }> }) => {
        const { actions } = inputParams;
        const results: any[] = [];

        for (const action of actions) {
          const { type, selector, value } = action;
          const element = document.querySelector(selector) as HTMLInputElement | HTMLTextAreaElement;

          if (!element) {
            results.push({
              action: action,
              success: false,
              error: 'Element not found'
            });
            continue;
          }

          try {
            switch (type) {
              case 'input':
                element.focus();

                // 处理React受控组件
                const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
                  window.HTMLInputElement.prototype, 'value'
                )?.set ||
                  Object.getOwnPropertyDescriptor(
                    window.HTMLTextAreaElement.prototype, 'value'
                  )?.set;

                if (nativeInputValueSetter && value !== undefined) {
                  nativeInputValueSetter.call(element, value);
                } else if (value !== undefined) {
                  element.value = value;
                }

                // 触发事件
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));
                break;

              case 'click':
                element.click();
                break;

              case 'focus':
                element.focus();
                break;
            }

            results.push({ action: action, success: true });

          } catch (error) {
            results.push({
              action: action,
              success: false,
              error: (error as Error).message
            });
          }
        }

        return { success: true, results };
      },
      args: [{ actions }],
      world: 'MAIN'
    });

    return results[0].result;
  }

  private async scrollPage(tabId: number, params: {
    direction?: string;
    amount?: number;
  }): Promise<ActionResult> {
    const { direction = 'down', amount = 500 } = params;

    const results = await chrome.scripting.executeScript({
      target: { tabId },
      func: (scrollParams: { direction: string; amount: number }) => {
        const { direction, amount } = scrollParams;

        const scrollOptions: ScrollToOptions = {
          top: direction === 'down' ? amount : -amount,
          behavior: 'smooth'
        };

        window.scrollBy(scrollOptions);

        return {
          success: true,
          newPosition: {
            x: window.scrollX,
            y: window.scrollY
          }
        };
      },
      args: [{ direction, amount }],
      world: 'MAIN'
    });

    return results[0].result;
  }

  private async executeScript(tabId: number, script: string): Promise<ActionResult> {
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      func: (code: string) => {
        try {
          const result = eval(code);
          return {
            success: true,
            result: result
          };
        } catch (error) {
          throw new Error(`脚本执行失败: ${(error as Error).message}`);
        }
      },
      args: [script],
      world: 'MAIN'
    });

    return results[0].result;
  }

  private async takeScreenshot(tabId: number, params: {
    format?: 'png' | 'jpeg';
    quality?: number;
  } = {}): Promise<ActionResult> {
    if (typeof chrome === 'undefined' || !chrome.tabs) {
      throw new Error('Chrome tabs API不可用');
    }

    const { format = 'png', quality = 90 } = params;
    const tab = await chrome.tabs.get(tabId);
    const dataUrl = await chrome.tabs.captureVisibleTab(tab.windowId, {
      format: format as any,
      quality
    });

    return {
      success: true,
      screenshot: dataUrl,
      tabId: tabId,
      timestamp: new Date().toISOString()
    };
  }

  private async sendHttpRequest(params: {
    method?: string;
    url?: string;
    headers?: Record<string, string>;
    data?: any;
    timeout?: number;
  } = {}): Promise<ActionResult> {
    const {
      method = 'GET',
      url,
      headers = {},
      data,
      timeout = 30000
    } = params;

    if (!url) {
      throw new Error('URL参数是必需的');
    }

    const fetchOptions: RequestInit = {
      method: method.toUpperCase(),
      headers: { ...headers }
    };

    if (data && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
      if (typeof data === 'object') {
        fetchOptions.body = JSON.stringify(data);
        if (!fetchOptions.headers) {
          fetchOptions.headers = {};
        }
        (fetchOptions.headers as Record<string, string>)['Content-Type'] = 'application/json';
      } else {
        fetchOptions.body = data;
      }
    }

    // 设置超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    fetchOptions.signal = controller.signal;

    try {
      const startTime = Date.now();
      const response = await fetch(url, fetchOptions);
      const duration = Date.now() - startTime;

      clearTimeout(timeoutId);

      let responseData: any;
      const contentType = response.headers.get('content-type') || '';

      if (contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      return {
        success: true,
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        duration: duration
      };

    } catch (error) {
      clearTimeout(timeoutId);

      if ((error as Error).name === 'AbortError') {
        throw new Error(`请求超时 (${timeout}ms)`);
      }

      throw error;
    }
  }

  // === 标签页管理 ===

  async getAllTabs(): Promise<TabInfo[]> {
    if (typeof chrome === 'undefined' || !chrome.tabs) {
      throw new Error('Chrome tabs API不可用');
    }

    const tabs = await chrome.tabs.query({});
    return tabs.map(tab => ({
      id: tab.id!,
      title: tab.title || '',
      url: tab.url || '',
      active: tab.active,
      windowId: tab.windowId
    }));
  }

  async switchTab(tabId: number): Promise<ActionResult> {
    if (typeof chrome === 'undefined' || !chrome.tabs) {
      throw new Error('Chrome tabs API不可用');
    }

    await chrome.tabs.update(tabId, { active: true });
    const tab = await chrome.tabs.get(tabId);
    await chrome.windows.update(tab.windowId, { focused: true });

    return {
      success: true,
      tabId: tabId,
      title: tab.title
    };
  }

  async openTab(url: string): Promise<ActionResult> {
    if (typeof chrome === 'undefined' || !chrome.tabs) {
      throw new Error('Chrome tabs API不可用');
    }

    const tab = await chrome.tabs.create({ url, active: true });

    return {
      success: true,
      tabId: tab.id!,
      url: tab.url
    };
  }

  async closeTab(tabId: number): Promise<ActionResult> {
    if (typeof chrome === 'undefined' || !chrome.tabs) {
      throw new Error('Chrome tabs API不可用');
    }

    await chrome.tabs.remove(tabId);

    return {
      success: true,
      tabId: tabId
    };
  }

  async navigatePage(tabId: number, url: string): Promise<ActionResult> {
    if (typeof chrome === 'undefined' || !chrome.tabs) {
      throw new Error('Chrome tabs API不可用');
    }

    await chrome.tabs.update(tabId, { url });

    return {
      success: true,
      tabId: tabId,
      url: url
    };
  }

  // === 工具方法 ===

  async disconnect(): Promise<void> {
    if (this.ws) {
      this.ws.close();
      this.isConnected = false;
    }
  }

  getConnectionStatus(): ConnectionStatus {
    return {
      connected: this.isConnected,
      clientId: this.clientId,
      serverUrl: this.serverUrl
    };
  }

  // === CDP真实硬件事件实现 ===

  /**
   * CDP真实点击实现
   */
  private async cdpRealClick(tabId: number, selector: string, coordinates?: { x: number; y: number }): Promise<ActionResult> {
    this.log('🚀 启动CDP真实鼠标点击');

    try {
      // 连接到标签页的debugger
      const debuggee = { tabId: tabId };

      // 启用必要的域
      await chrome.debugger.attach(debuggee, '1.2');
      await chrome.debugger.sendCommand(debuggee, 'Runtime.enable');
      await chrome.debugger.sendCommand(debuggee, 'DOM.enable');

      this.log('✅ CDP连接建立成功');

      let clickX: number, clickY: number, elementInfo: any;

      if (coordinates) {
        // 直接使用提供的坐标
        clickX = coordinates.x;
        clickY = coordinates.y;
        this.log(`📍 使用提供的坐标: (${clickX}, ${clickY})`);

        // 获取该位置的元素信息
        elementInfo = await this.getElementInfoAtPoint(debuggee, clickX, clickY);
      } else if (selector) {
        // 通过选择器查找元素并获取位置
        const elementData = await this.getElementPositionBySelector(debuggee, selector);
        clickX = elementData.centerX;
        clickY = elementData.centerY;
        elementInfo = elementData.info;
        this.log(`📍 通过选择器找到位置: (${clickX}, ${clickY})`);
      } else {
        throw new Error('必须提供selector或coordinates参数');
      }

      // 执行真实的鼠标点击
      await this.cdpRealMouseClick(debuggee, clickX, clickY);

      // 断开debugger连接
      await chrome.debugger.detach(debuggee);
      this.log('✅ CDP连接已断开');

      return {
        success: true,
        method: 'chrome_debugger_cdp',
        position: { x: clickX, y: clickY },
        element: elementInfo || { message: '未获取到元素信息' }
      };

    } catch (error) {
      this.log('❌ CDP真实鼠标点击失败:', error);

      // 尝试断开连接
      try {
        await chrome.debugger.detach({ tabId: tabId });
      } catch (detachError) {
        // 忽略断开连接的错误
      }

      throw new Error(`CDP点击失败: ${(error as Error).message}`);
    }
  }

  /**
   * CDP用户输入模拟
   */
  private async cdpSimulateUserInput(tabId: number, actions: Array<{ type: string; selector: string; value?: string }>): Promise<ActionResult> {
    this.log('🔍 CDP参数调试:', { tabId, actions });

    if (!Array.isArray(actions)) {
      throw new Error(`actions必须是数组，收到的类型: ${typeof actions}`);
    }

    try {
      this.log('🚀 启动CDP真实用户行为模拟');

      // 连接到标签页的debugger
      const debuggee = { tabId: tabId };

      // 启用必要的域
      await chrome.debugger.attach(debuggee, '1.2');
      await chrome.debugger.sendCommand(debuggee, 'Runtime.enable');
      await chrome.debugger.sendCommand(debuggee, 'DOM.enable');

      this.log('✅ CDP连接建立成功');

      const results = [];

      for (const action of actions) {
        try {
          const result = await this.executeRealUserAction(debuggee, action);
          results.push(result);
        } catch (error) {
          results.push({
            action: action,
            success: false,
            error: (error as Error).message,
            method: 'cdp_failed'
          });
        }
      }

      // 断开debugger连接
      await chrome.debugger.detach(debuggee);
      this.log('✅ CDP连接已断开');

      return {
        success: true,
        results: results,
        method: 'chrome_debugger_cdp'
      };

    } catch (error) {
      this.log('❌ CDP真实用户行为模拟失败:', error);

      // 尝试断开连接
      try {
        await chrome.debugger.detach({ tabId: tabId });
      } catch (detachError) {
        // 忽略断开连接的错误
      }

      throw new Error(`CDP模拟失败: ${(error as Error).message}`);
    }
  }

  /**
   * 使用CDP执行单个真实用户操作
   */
  private async executeRealUserAction(debuggee: { tabId: number }, action: { type: string; selector: string; value?: string }): Promise<any> {
    const { type, selector, value } = action;

    this.log(`👆 CDP真实操作: ${type} on ${selector}`);

    try {
      // 1. 获取文档根节点
      const docResult = await chrome.debugger.sendCommand(debuggee, 'DOM.getDocument', {
        depth: -1,  // 获取完整的DOM树
        pierce: true  // 穿透shadow DOM
      }) as CDPResult;

      // 2. 查找目标元素
      let nodeId;
      try {
        const result = await chrome.debugger.sendCommand(debuggee, 'DOM.querySelector', {
          nodeId: docResult.root.nodeId,
          selector: selector
        }) as CDPResult;
        nodeId = result.nodeId;
      } catch (querySelectorError) {
        // 尝试用Runtime.evaluate作为备选方案
        const evalResult = await chrome.debugger.sendCommand(debuggee, 'Runtime.evaluate', {
          expression: `document.querySelector('${selector.replace(/'/g, "\\'")}')`,
          returnByValue: false
        }) as CDPResult;

        if (evalResult.result && evalResult.result.objectId) {
          const nodeResult = await chrome.debugger.sendCommand(debuggee, 'DOM.requestNode', {
            objectId: evalResult.result.objectId
          }) as CDPResult;
          nodeId = nodeResult.nodeId;
        } else {
          throw new Error(`所有方法都无法找到元素: ${selector}`);
        }
      }

      if (!nodeId) {
        throw new Error(`元素未找到: ${selector}`);
      }

      // 3. 获取元素的边界框
      const boxResult = await chrome.debugger.sendCommand(debuggee, 'DOM.getBoxModel', {
        nodeId: nodeId
      }) as CDPResult;

      if (!boxResult.model) {
        throw new Error('无法获取元素位置');
      }

      // 计算元素中心点
      const [x1, y1, x2, y2] = boxResult.model.content;
      const centerX = (x1 + x2) / 2;
      const centerY = (y1 + y2) / 2;

      this.log(`📍 元素位置: (${centerX}, ${centerY})`);

      switch (type) {
        case 'input':
          await this.cdpRealTextInput(debuggee, centerX, centerY, value!);
          break;

        case 'click':
          await this.cdpRealMouseClick(debuggee, centerX, centerY);
          break;

        case 'focus':
          await this.cdpRealMouseClick(debuggee, centerX, centerY);
          break;

        default:
          throw new Error(`CDP不支持的操作类型: ${type}`);
      }

      return {
        action: action,
        success: true,
        method: 'cdp_real_events',
        position: { x: centerX, y: centerY }
      };

    } catch (error) {
      this.log(`❌ CDP操作失败:`, error);
      throw error;
    }
  }

  /**
   * CDP真实文本输入
   */
  private async cdpRealTextInput(debuggee: { tabId: number }, x: number, y: number, text: string): Promise<void> {
    this.log(`⌨️ CDP真实键盘输入: "${text}"`);

    // 1. 先点击聚焦到元素
    await this.cdpRealMouseClick(debuggee, x, y);

    // 2. 全选现有内容（Ctrl+A）
    await chrome.debugger.sendCommand(debuggee, 'Input.dispatchKeyEvent', {
      type: 'keyDown',
      modifiers: 2, // Ctrl键
      key: 'a',
      code: 'KeyA',
      windowsVirtualKeyCode: 65
    });

    await chrome.debugger.sendCommand(debuggee, 'Input.dispatchKeyEvent', {
      type: 'keyUp',
      modifiers: 2,
      key: 'a',
      code: 'KeyA',
      windowsVirtualKeyCode: 65
    });

    // 3. 逐字符输入文本（真实键盘事件）
    for (const char of text) {
      // 简化的键盘事件，兼容中文字符
      await chrome.debugger.sendCommand(debuggee, 'Input.dispatchKeyEvent', {
        type: 'char',
        text: char
      });

      // 小延迟，模拟真实打字速度
      await new Promise(resolve => setTimeout(resolve, 30));
    }

    this.log(`✅ CDP真实输入完成: "${text}"`);
  }

  /**
   * CDP真实鼠标点击
   */
  private async cdpRealMouseClick(debuggee: { tabId: number }, x: number, y: number): Promise<void> {
    this.log(`🖱️ CDP真实鼠标点击: (${x}, ${y})`);

    // mousePressed事件
    await chrome.debugger.sendCommand(debuggee, 'Input.dispatchMouseEvent', {
      type: 'mousePressed',
      x: x,
      y: y,
      button: 'left',
      clickCount: 1
    });

    // 小延迟
    await new Promise(resolve => setTimeout(resolve, 100));

    // mouseReleased事件
    await chrome.debugger.sendCommand(debuggee, 'Input.dispatchMouseEvent', {
      type: 'mouseReleased',
      x: x,
      y: y,
      button: 'left',
      clickCount: 1
    });

    this.log('✅ CDP真实点击完成');
  }

  /**
   * 通过选择器获取元素位置
   */
  private async getElementPositionBySelector(debuggee: { tabId: number }, selector: string): Promise<{ centerX: number; centerY: number; info: any }> {
    // 获取完整DOM树
    const docResult = await chrome.debugger.sendCommand(debuggee, 'DOM.getDocument', {
      depth: -1,
      pierce: true
    }) as CDPResult;

    // 查找目标元素
    let nodeId;
    try {
      const result = await chrome.debugger.sendCommand(debuggee, 'DOM.querySelector', {
        nodeId: docResult.root.nodeId,
        selector: selector
      }) as CDPResult;
      nodeId = result.nodeId;
    } catch (querySelectorError) {
      // 使用Runtime.evaluate作为备选方案
      const evalResult = await chrome.debugger.sendCommand(debuggee, 'Runtime.evaluate', {
        expression: `document.querySelector('${selector.replace(/'/g, "\\'")}')`,
        returnByValue: false
      }) as CDPResult;

      if (evalResult.result && evalResult.result.objectId) {
        const nodeResult = await chrome.debugger.sendCommand(debuggee, 'DOM.requestNode', {
          objectId: evalResult.result.objectId
        }) as CDPResult;
        nodeId = nodeResult.nodeId;
      }
    }

    if (!nodeId) {
      throw new Error(`元素未找到: ${selector}`);
    }

    // 获取元素的边界框
    const boxResult = await chrome.debugger.sendCommand(debuggee, 'DOM.getBoxModel', {
      nodeId: nodeId
    }) as CDPResult;

    if (!boxResult.model) {
      throw new Error('无法获取元素位置');
    }

    // 计算元素中心点
    const [x1, y1, x2, y2] = boxResult.model.content;
    const centerX = (x1 + x2) / 2;
    const centerY = (y1 + y2) / 2;

    // 获取元素基本信息
    const attributesResult = await chrome.debugger.sendCommand(debuggee, 'DOM.getAttributes', {
      nodeId: nodeId
    }) as CDPResult;

    const info = {
      nodeId: nodeId,
      attributes: attributesResult.attributes,
      position: { x1, y1, x2, y2 }
    };

    return { centerX, centerY, info };
  }

  /**
   * 获取指定位置的元素信息
   */
  private async getElementInfoAtPoint(debuggee: { tabId: number }, x: number, y: number): Promise<any> {
    try {
      const evalResult = await chrome.debugger.sendCommand(debuggee, 'Runtime.evaluate', {
        expression: `
          const element = document.elementFromPoint(${x}, ${y});
          if (element) {
            {
              tagName: element.tagName,
              id: element.id,
              className: element.className,
              text: element.textContent?.trim().substring(0, 50)
            }
          } else {
            null
          }
        `,
        returnByValue: true
      }) as CDPResult;

      return evalResult.result.value;
    } catch (error) {
      this.log('获取元素信息失败:', error);
      return null;
    }
  }

  // Getter方法
  get connected(): boolean {
    return this.isConnected;
  }

  get id(): string {
    return this.clientId;
  }

  get cdpEnabled(): boolean {
    return this.enableCDP;
  }
}
