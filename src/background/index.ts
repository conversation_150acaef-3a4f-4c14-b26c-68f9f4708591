/**
 * Service Worker：用来监听事件、发送消息
 */
import { EContentsMessageType, MessageType } from '@src/common/const'
import { EIP_CONFIG } from '@src/common/const'
import { handleFloatingButtonAction } from './components/floatingButton'
import { BrowserToolSDK } from './utils/browser-tool-sdk';
import type { BrowserToolSDKOptions } from './utils/browser-tool-sdk';
import { getUserId } from '@src/common/utils/userConfigApi';
import { getBrowserToolWebSocketUrl } from '@src/common/utils/baseUrl';
import { executeXzlParamExtraction } from "@src/contents/scripts/xzlBzParamsExtract";

// === Browser Tool SDK 相关 ===
let browserToolSDK: BrowserToolSDK | null = null;
let currentUserId: string | null = null;

// Browser Tool SDK 基础配置
const BROWSER_TOOL_SDK_BASE_CONFIG: Omit<BrowserToolSDKOptions, 'clientId'> = {
  serverUrl: getBrowserToolWebSocketUrl(),
  autoConnect: true,
  autoReconnect: true,
  reconnectDelay: 3000,
  enableLogging: true,
  enableCDP: false
};

// 生成基于用户ID的客户端ID
function generateUserClientId(userId: string): string {
  const randomSuffix = Math.random().toString(36).substr(2, 9);
  return `user_${userId}_${randomSuffix}`;
}

// 初始化Browser Tool SDK
async function initBrowserToolSDK(userId: string) {
  try {
    // 如果已经初始化且用户ID相同，直接返回
    if (browserToolSDK && currentUserId === userId) {
      console.log('🔄 Browser Tool SDK 已初始化，用户ID相同，跳过重新初始化');
      return;
    }

    // 如果已存在SDK但用户ID不同，先断开
    if (browserToolSDK && currentUserId !== userId) {
      console.log('🔄 用户切换，重新初始化Browser Tool SDK');
      await browserToolSDK.disconnect();
      browserToolSDK = null;
    }

    const clientId = generateUserClientId(userId);
    const config: BrowserToolSDKOptions = {
      ...BROWSER_TOOL_SDK_BASE_CONFIG,
      clientId: clientId
    };

    browserToolSDK = new BrowserToolSDK(config);
    currentUserId = userId;

    console.log(`🚀 Browser Tool SDK 初始化成功 - 用户: ${userId}, ClientId: ${clientId}`);
  } catch (error) {
    console.error('❌ Browser Tool SDK 初始化失败:', error);
  }
}

// 清理Browser Tool SDK
async function cleanupBrowserToolSDK() {
  try {
    if (browserToolSDK) {
      await browserToolSDK.disconnect();
      browserToolSDK = null;
      currentUserId = null;
      console.log('🧹 Browser Tool SDK 已清理');
    }
  } catch (error) {
    console.error('❌ Browser Tool SDK 清理失败:', error);
  }
}

// 检查用户登录状态并初始化SDK
async function checkUserLoginAndInit() {
  try {
    console.log('🔍 检查用户登录状态...');
    const userId = await getUserId();

    console.log('[checkUserLoginAndInit] getUserId返回值:', userId, '类型:', typeof userId);

    if (userId && userId !== 'anonymous_user') {
      console.log(`👤 发现已登录用户: ${userId}`);
      await initBrowserToolSDK(userId);
    } else {
      console.log(`❌ 用户未登录 (userId=${userId})，等待登录后初始化...`);
    }
  } catch (error) {
    console.error('❌ 检查用户登录状态失败:', error);
    console.log('⏳ 将通过登录接口监听来初始化SDK...');
  }
}

// 判断是否为Browser Tool消息
function isBrowserToolMessage(message: any): boolean {
  return message.source === 'browserTool';
}

// 监听 Storage 变化，当用户信息更新时初始化SDK
chrome.storage.onChanged.addListener((changes, areaName) => {
  if (areaName === 'local' && changes.userInfo) {
    const newUserInfo = changes.userInfo.newValue;
    const oldUserInfo = changes.userInfo.oldValue;

    // 从无到有（登录）
    if (!oldUserInfo && newUserInfo) {
      console.log('🔑 检测到用户登录，用户信息已保存:', newUserInfo);

      // 提取userId
      const userId = newUserInfo.userId || newUserInfo.id || newUserInfo.userName;
      if (userId && userId !== 'anonymous_user') {
        console.log('👤 开始初始化Browser Tool SDK，用户:', userId);
        initBrowserToolSDK(userId).then(() => {
          console.log('✅ 登录后Browser Tool SDK初始化完成');
        }).catch(error => {
          console.error('❌ 登录后Browser Tool SDK初始化失败:', error);
        });
      }
    }

    // 从有到无（登出）
    else if (oldUserInfo && !newUserInfo) {
      console.log('🚪 检测到用户登出，清理Browser Tool SDK...');
      cleanupBrowserToolSDK().catch(error => {
        console.error('❌ 登出后Browser Tool SDK清理失败:', error);
      });
    }

    // 用户切换
    else if (oldUserInfo && newUserInfo) {
      const oldUserId = oldUserInfo.userId || oldUserInfo.id || oldUserInfo.userName;
      const newUserId = newUserInfo.userId || newUserInfo.id || newUserInfo.userName;

      if (oldUserId !== newUserId) {
        console.log(`🔄 检测到用户切换: ${oldUserId} -> ${newUserId}`);
        initBrowserToolSDK(newUserId).then(() => {
          console.log('✅ 用户切换后Browser Tool SDK重新初始化完成');
        }).catch(error => {
          console.error('❌ 用户切换后Browser Tool SDK初始化失败:', error);
        });
      }
    }
  }
});

// 监听来自内容脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // 处理获取Browser Tool ClientId的请求
  if (message.type === 'getBrowserToolClientId') {
    if (browserToolSDK && browserToolSDK.id) {
      console.log('[background] 返回Browser Tool ClientId:', browserToolSDK.id);
      sendResponse({ success: true, clientId: browserToolSDK.id });
    } else {
      console.warn('[background] Browser Tool SDK 未初始化或没有clientId');
      sendResponse({ success: false, clientId: '', message: 'Browser Tool SDK 未初始化' });
    }
    return true; // 保持异步响应
  }

  // 检查是否为Browser Tool相关消息
  if (isBrowserToolMessage(message)) {
    if (!browserToolSDK) {
      console.error('Browser Tool SDK 未初始化，尝试重新检查用户登录状态...');
      // 尝试重新检查用户登录状态
      checkUserLoginAndInit().then(() => {
        if (browserToolSDK) {
          console.log('✅ SDK重新初始化成功，请重试操作');
          sendResponse({ success: true, message: 'SDK已重新初始化，请重试操作' });
        } else {
          sendResponse({ success: false, error: 'Browser Tool SDK 初始化失败，用户可能未登录' });
        }
      }).catch((error) => {
        sendResponse({ success: false, error: `Browser Tool SDK 初始化失败: ${error.message}` });
      });
      return true; // 保持异步响应
    }

    // Browser Tool SDK 已初始化，可以正常使用
    console.log('🔧 Browser Tool SDK 可用，消息:', message);
    sendResponse({ success: true, message: 'Browser Tool SDK 已就绪', clientId: browserToolSDK.id });
    return true; // 保持异步响应
  }

  // 处理悬浮球的操作
  if (message.type === EContentsMessageType.FloatingButton) {
    handleFloatingButtonAction(message, sender, sendResponse)
    return true
  }

  // 处理取消翻译消息，转发到content script
  if (message.type === MessageType.CANCEL_TRANSLATE) {
    console.log('Background收到CANCEL_TRANSLATE消息，转发到content script')
    if (sender.tab?.id) {
      chrome.tabs.sendMessage(sender.tab.id, {
        type: MessageType.CANCEL_TRANSLATE
      }).catch(error => {
        console.error('转发CANCEL_TRANSLATE消息失败:', error)
      })
    }
    return true
  }

  // 处理开始翻译消息，转发到content script
  if (message.type === MessageType.START_TRANSLATE) {
    console.log('Background收到START_TRANSLATE消息，转发到content script')
    if (sender.tab?.id) {
      chrome.tabs.sendMessage(sender.tab.id, {
        type: MessageType.START_TRANSLATE
      }).catch(error => {
        console.error('转发START_TRANSLATE消息失败:', error)
      })
    }
    return true
  }

  // 处理显示 DiffModal 消息，转发到content script
  if (message.type === 'SHOW_DIFF_MODAL_TO_BACKGROUND') {
    console.log('Background收到SHOW_DIFF_MODAL_TO_BACKGROUND消息，转发到content script')
    const tabId = message.tabId || sender.tab?.id;
    if (tabId) {
      chrome.tabs.sendMessage(tabId, {
        type: 'SHOW_DIFF_MODAL',
        data: message.data
      }).then((response) => {
        // 将content script的响应返回给sidepanel
        sendResponse(response);
      }).catch((error) => {
        console.error('转发SHOW_DIFF_MODAL消息失败:', error);
        sendResponse({ success: false, error: '转发消息到content script失败' });
      });
    } else {
      sendResponse({ success: false, error: '无法获取标签页ID' });
    }
    return true // 保持消息通道开放以进行异步响应
  }

  // 处理划词工具栏的操作
  if (message.action) {
    console.log(
      '收到划词工具栏操作:',
      message.action,
      '选中文本:',
      message.text
    )

    switch (message.action) {
      case 'open-panel':
        // 打开侧边栏面板
        chrome.sidePanel.open({ windowId: sender.tab?.windowId })
        break

      case 'summary':
        // 处理总结操作
        handleSummaryAction(message.text, sender.tab?.id)
        break

      case 'translate':
        // 处理翻译操作
        handleTranslateAction(message.text, sender.tab?.id)
        break

      case 'text-condenser':
        // 处理缩写操作
        handleTextOperation(message.text, '缩写', sender.tab?.id)
        break

      case 'text-expander':
        // 处理扩写操作
        handleTextOperation(message.text, '扩写', sender.tab?.id)
        break

      case 'text-polisher':
        // 处理润色操作
        handleTextOperation(message.text, '润色', sender.tab?.id)
        break

      case 'grammar-corrector':
        // 处理修正拼写和语义操作
        handleTextOperation(message.text, '修正拼写和语义', sender.tab?.id)
        break

      case 'CONTINUE_ASK_TO_BACKGROUND':
        // 处理继续问操作
        handleContinueAsk(
          message.question,
          message.originalText,
          message.originalAction,
          message.conversationId,
          sender.tab?.windowId
        )
        break
    }
  }

  // 处理来自content script的提取结果
  if (message.type === 'XZL_PARAM_EXTRACTION_RESULT') {
    // 转发给sidepanel
    chrome.runtime.sendMessage({
      type: 'XZL_PARAM_EXTRACTION_RESULT_TO_SIDEPANEL',
      data: message.data
    }).catch(err => {
      console.error('转发参数提取结果到sidepanel失败:', err);
    });
    sendResponse({ success: true }); // 添加响应
    return true; // 保持消息通道开放
  }
  if (message.type === 'openSettings') {
    chrome.tabs.create({
      url: `chrome-extension://${chrome.runtime.id}/tabs/settings.html`
    });
    sendResponse({ success: true }); // 添加响应
    return true;
  }

  return true // 表示异步响应
})

// 处理总结操作
function handleSummaryAction(text: string, tabId?: number) {
  // 这里可以实现总结功能，例如调用AI API进行总结
  console.log('执行总结操作:', text)

  // 示例：向侧边栏发送消息，请求总结
  chrome.runtime.sendMessage({
    type: 'PROCESS_TEXT',
    operation: '总结',
    text: text,
  })
}

// 处理翻译操作
function handleTranslateAction(text: string, tabId?: number) {
  // 这里可以实现翻译功能，例如调用翻译API
  console.log('执行翻译操作:', text)

  // 示例：向侧边栏发送消息，请求翻译
  chrome.runtime.sendMessage({
    type: 'PROCESS_TEXT',
    operation: '翻译',
    text: text,
  })
}

// 处理文本操作（缩写、扩写、润色、修正等）
function handleTextOperation(text: string, operation: string, tabId?: number) {
  console.log(`执行${operation}操作:`, text)

  // 示例：向侧边栏发送消息，请求处理文本
  chrome.runtime.sendMessage({
    type: 'PROCESS_TEXT',
    operation: operation,
    text: text,
  })
}

// 处理继续问操作
async function handleContinueAsk(
  question: string,
  originalText: string,
  originalAction: string,
  conversationId?: string,
  windowId?: number
) {
  console.log(
    '执行继续问操作:',
    question,
    '原文:',
    originalText,
    '原操作:',
    originalAction,
    'conversationId:',
    conversationId
  )

  // 打开侧边栏面板
  if (windowId) {
    await chrome.sidePanel.open({ windowId })

  }

  setTimeout(() => {
    // 向侧边栏发送消息，传递继续问的内容
    chrome.runtime.sendMessage({
      type: 'CONTINUE_ASK_TO_SIDEBAR',
      question: question,
      originalText: originalText,
      originalAction: originalAction,
      conversationId: conversationId,
    }).catch(error => {
      console.log('Failed to send message to sidebar:', error);
    });
  }, 1500)
}

// 监听登录接口调用
chrome.webRequest.onCompleted.addListener(
  async function (details) {
    if (details.url.includes('/gateway/login') && details.statusCode === 200) {
      console.log('检测到登录接口调用:', details)

      // 发送登录成功消息
      // Browser Tool SDK 的初始化将在消息监听器中处理
      chrome.runtime.sendMessage({
        type: 'loginSuccess',
        payload: {
          tabId: details.tabId
        },
      })
    }
  },
  { urls: [`${EIP_CONFIG.LOGIN_API}*`] },
  ['responseHeaders'] // 添加这个来获取响应头
)

// 监听登出接口调用
chrome.webRequest.onCompleted.addListener(
  async function (details) {
    if (details.url.includes('gateway/logout')) {
      console.log('检测到登出接口调用:', details)

      // 发送登出成功消息
      // Browser Tool SDK 的清理将在消息监听器中处理
      chrome.runtime.sendMessage({
        type: 'logoutSuccess',
        payload: { tabId: details.tabId },
      })
    }
  },
  { urls: [`${EIP_CONFIG.LOGOUT_API}*`] }
)

// 监听Wiki登录接口调用
console.log('[Background] 正在注册Wiki登录监听器...')

// 监听登录请求开始（用于调试确认）
chrome.webRequest.onBeforeRequest.addListener(
  function (details) {
    console.log('[Background] 检测到Wiki登录请求:', details.method, details.url)
  },
  {
    urls: [
      'http://wiki.htzq.htsc.com.cn/dologin.action*',
      'https://wiki.htzq.htsc.com.cn/dologin.action*'
    ]
  }
)

// 监听Wiki登录重定向（核心功能）
chrome.webRequest.onBeforeRedirect.addListener(
  function (details) {
    console.log('[Background] Wiki登录成功，重定向到:', details.redirectUrl)
    chrome.runtime.sendMessage({
      type: 'wikiLoginSuccess',
      payload: {
        tabId: details.tabId,
        statusCode: details.statusCode,
        url: details.url,
        redirectUrl: details.redirectUrl
      },
    }).catch(error => {
      console.error('[Background] 发送Wiki登录成功消息失败:', error)
    })
  },
  {
    urls: [
      'http://wiki.htzq.htsc.com.cn/dologin.action*',
      'https://wiki.htzq.htsc.com.cn/dologin.action*'
    ]
  }
)

console.log('[Background] ✅ Wiki登录监听器注册完成')

// 监听标签页更新事件
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  // 当标签页URL发生变化时检查是否匹配特定的hash
  if (changeInfo.url || (changeInfo.status === 'complete' && tab.url)) {
    const url = changeInfo.url || tab.url;
    if (url) {
      try {
        const urlObj = new URL(url);
        // 检查hash是否为指定值
        if (urlObj.hash.startsWith('#/dialogue-analysis/')) {
          // 通知侧边栏显示XZL参数提取组件
          chrome.runtime.sendMessage({
            type: 'SHOW_XZL_COMPONENT',
            showXZLComponent: true
          }).catch(err => {
            // 忽略错误，因为可能侧边栏未打开
          });
        } else {
          // 通知侧边栏显示引导页面
          chrome.runtime.sendMessage({
            type: 'SHOW_XZL_COMPONENT',
            showXZLComponent: false
          }).catch(err => {
            // 忽略错误，因为可能侧边栏未打开
          });
        }
      } catch (e) {
        // URL解析失败，忽略
        console.debug('Failed to parse URL:', url, e);
      }
    }
  }
});

// 监听标签页激活事件（用户切换标签页时触发）
chrome.tabs.onActivated.addListener(async (activeInfo) => {
  try {
    // 获取激活的标签页信息
    const tab = await chrome.tabs.get(activeInfo.tabId);

    if (tab.url) {
      const urlObj = new URL(tab.url);
      // 检查hash是否为指定值
      if (urlObj.hash.startsWith('#/dialogue-analysis/')) {
        // 通知侧边栏显示XZL参数提取组件
        chrome.runtime.sendMessage({
          type: 'SHOW_XZL_COMPONENT',
          showXZLComponent: true
        }).catch(err => {
          // 忽略错误，因为可能侧边栏未打开
        });
      } else {
        // 通知侧边栏显示引导页面
        chrome.runtime.sendMessage({
          type: 'SHOW_XZL_COMPONENT',
          showXZLComponent: false
        }).catch(err => {
          // 忽略错误，因为可能侧边栏未打开
        });
      }
    }
  } catch (e) {
    // 获取标签页信息失败，忽略
    console.debug('Failed to get tab info:', activeInfo, e);
  }
});

// 处理新涨乐参数提取请求的函数
async function handleXZLParamExtractionRequest(sendResponse: (response: any) => void) {
  try {
    // 获取当前活动标签页
    const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });

    if (!activeTab || !activeTab.id) {
      sendResponse({ success: false, error: '无法获取当前活动标签页' });
      return;
    }

    // 在当前标签页执行参数提取
    const result = await chrome.scripting.executeScript({
      target: { tabId: activeTab.id },
      func: executeXzlParamExtraction,
    });

    // 返回结果
    if (result && result[0] && result[0].result) {
      sendResponse(result[0].result);
    } else {
      sendResponse({ success: false, error: '参数提取执行失败' });
    }
  } catch (error) {
    console.error('执行新涨乐参数提取时出错:', error);
    sendResponse({ success: false, error: `执行失败: ${(error as Error).message}` });
  }
}

// Add message listener
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // 处理来自sidepanel的新涨乐参数提取请求
  if (message.type === 'XZL_PARAM_EXTRACTION_REQUEST_FROM_SIDEPANEL') {
    // 将请求转发到当前活动标签页的content script
    chrome.tabs.query({ active: true, currentWindow: true }).then(([activeTab]) => {
      if (activeTab && activeTab.id) {
        chrome.tabs.sendMessage(activeTab.id, {
          type: 'XZL_PARAM_EXTRACTION_REQUEST_FROM_SIDEPANEL'
        }).then((response) => {
          // 将content script的响应返回给sidepanel
          sendResponse(response);
        }).catch((error) => {
          console.error('转发消息到content script失败:', error);
          sendResponse({ success: false, error: '转发消息到content script失败' });
        });
      } else {
        sendResponse({ success: false, error: '无法获取当前活动标签页' });
      }
    });
    return true; // 保持消息通道开放以进行异步响应
  }
});

// === Browser Tool SDK 生命周期事件 ===
// 插件启动时检查用户登录状态
console.log('⚡ 启动Browser Tool SDK初始化检查...');
checkUserLoginAndInit();
