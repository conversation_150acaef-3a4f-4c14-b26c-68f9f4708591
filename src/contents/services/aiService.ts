/**
 * AI 服务层 - 处理与 AI 服务端的通信
 */

export interface AIRequest {
  action: string;
  text: string;
  options?: Record<string, any>;
}

export interface AIStreamResponse {
  content: string;
  isComplete: boolean;
  error?: string;
}

export type AIStreamCallback = (response: AIStreamResponse) => void;

/**
 * AI 服务类
 */
export class AIService {
  private baseUrl: string;
  private apiKey?: string;

  constructor(baseUrl: string = 'http://localhost:3000/api', apiKey?: string) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
  }

  /**
   * 发送 AI 处理请求（SSE 流式响应）
   */
  async processText(
    request: AIRequest,
    onStream: AIStreamCallback,
    onError?: (error: Error) => void
  ): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/ai/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.apiKey && { 'Authorization': `Bearer ${this.apiKey}` })
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Response body is not readable');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          // 处理完成
          onStream({ content: '', isComplete: true });
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // 保留不完整的行

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              onStream({
                content: data.content || '',
                isComplete: data.isComplete || false,
                error: data.error
              });
            } catch (e) {
              console.warn('Failed to parse SSE data:', line);
            }
          }
        }
      }
    } catch (error) {
      console.error('AI service error:', error);
      if (onError) {
        onError(error as Error);
      } else {
        onStream({
          content: '',
          isComplete: true,
          error: (error as Error).message
        });
      }
    }
  }

  /**
   * 获取支持的操作类型
   */
  getSupportedActions(): string[] {
    return ['summary', 'translate', 'text-condenser', 'text-expander', 'text-polisher', 'grammar-corrector'];
  }

  /**
   * 获取操作类型的中文名称
   */
  getActionName(action: string): string {
    const actionMap: Record<string, string> = {
      'summary': '总结',
      'translate': '翻译',
      'text-condenser': '缩写',
      'text-expander': '扩写',
      'text-polisher': '润色',
      'grammar-corrector': '修正',
      'xzl-param-tag': '新涨乐参数打标'
    };

    // 处理自定义技能
    if (action && action.includes('custom-skill-')) {
      return '自定义技能';
    }

    return actionMap[action] || action || '未知操作';
  }
}

/**
 * 默认的 AI 服务实例
 */
export const aiService = new AIService();
