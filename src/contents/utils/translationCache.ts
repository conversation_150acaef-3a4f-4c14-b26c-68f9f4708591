/**
 * 翻译缓存管理工具
 * 使用Chrome Storage API实现翻译结果的缓存功能
 * 简化版本，只提供核心的存储和获取功能
 */

// 翻译缓存项的类型定义
interface TranslationCacheItem {
  originalText: string
  translatedText: string
  timestamp: number
}

// 存储键名
const STORAGE_KEY = 'ai_extension_translation_cache'
// 缓存过期时间（24小时）
const CACHE_EXPIRE_TIME = 24 * 60 * 60 * 1000

/**
 * 生成缓存键
 */
function generateCacheKey(originalText: string): string {
  return originalText.trim().toLowerCase()
}

/**
 * 检查缓存项是否过期
 */
function isExpired(item: TranslationCacheItem): boolean {
  const now = Date.now()
  return (now - item.timestamp) > CACHE_EXPIRE_TIME
}

/**
 * 从Chrome Storage获取翻译缓存
 * @param originalText 原文文本
 * @returns 翻译结果，如果不存在或已过期则返回null
 */
export async function getTranslationFromCache(originalText: string): Promise<string | null> {
  try {
    const cacheKey = generateCacheKey(originalText)
    const result = await chrome.storage.local.get([STORAGE_KEY])
    const cache = result[STORAGE_KEY] || {}
    const cacheItem = cache[cacheKey]

    if (!cacheItem) {
      return null
    }

    // 检查是否过期
    if (isExpired(cacheItem)) {
      // 删除过期缓存
      delete cache[cacheKey]
      await chrome.storage.local.set({ [STORAGE_KEY]: cache })
      return null
    }

    return cacheItem.translatedText
  } catch (error) {
    console.error('[TranslationCache] 获取缓存失败:', error)
    return null
  }
}

/**
 * 存储翻译结果到Chrome Storage缓存
 * @param originalText 原文文本
 * @param translatedText 翻译结果
 */
export async function setTranslationToCache(originalText: string, translatedText: string): Promise<void> {
  try {
    const cacheKey = generateCacheKey(originalText)
    const now = Date.now()

    const result = await chrome.storage.local.get([STORAGE_KEY])
    const cache = result[STORAGE_KEY] || {}

    // 存储新的翻译缓存
    cache[cacheKey] = {
      originalText,
      translatedText,
      timestamp: now
    }

    await chrome.storage.local.set({ [STORAGE_KEY]: cache })
  } catch (error) {
    console.error('[TranslationCache] 存储缓存失败:', error)
  }
}

/**
 * 清除所有翻译缓存
 */
export async function clearTranslationCache(): Promise<void> {
  try {
    await chrome.storage.local.remove([STORAGE_KEY])
  } catch (error) {
    console.error('[TranslationCache] 清除缓存失败:', error)
  }
}