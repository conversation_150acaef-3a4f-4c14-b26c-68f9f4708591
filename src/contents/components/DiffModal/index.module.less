.modalOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9998;
    animation: fadeIn 0.2s ease-in-out;

    &.closing {
        animation: fadeOut 0.2s ease-in-out;
    }
}

.modal {
    background: white;
    border-radius: 8px;
    width: 90vw;
    max-width: 1400px;
    height: 90vh;
    max-height: 900px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.2s ease-in-out;

    &.closing {
        animation: slideOut 0.2s ease-in-out;
    }
}

.modalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #e8e8e8;
    flex-shrink: 0;
}

.modalTitle {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.closeButton {
    background: none;
    border: none;
    font-size: 28px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
        background-color: #f5f5f5;
        color: #333;
    }
}

.modalContent {
    flex: 1;
    overflow: auto;
    padding: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

@keyframes slideIn {
    from {
        transform: scale(0.9);
        opacity: 0;
    }

    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: scale(1);
        opacity: 1;
    }

    to {
        transform: scale(0.9);
        opacity: 0;
    }
}