.diffPage {
  display: grid;
  gap: 24px;
  padding: 24px;
  background: linear-gradient(180deg, #f7faff 0%, #eef2ff 100%);
  border-radius: 20px;
  border: 1px solid #e3e8f4;
  box-shadow: 0 12px 32px rgba(15, 23, 42, 0.08);
  height: 100%;
  max-height: calc(100vh - 48px);
  grid-template-rows: auto 1fr;

  >div {
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 100%;
    overflow: hidden;
  }
}

.toolbar {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
  padding: 16px 24px;
  background: #fff;
  border-radius: 14px;
  border: 1px solid #e1e7f5;
  box-shadow: 0 8px 20px rgba(100, 116, 139, 0.08);
}

.modePanel {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.modeGroup {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.modeOptions {
  display: inline-flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.modeHint {
  margin: 8px 0;
  font-size: 12px;
  color: #64748b;
}

.subModeContainer {
  display: inline-flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.subModeGroup {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.subModeLabel {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.clearButton {
  padding: 6px 14px;
  border-radius: 999px;
  border: 1px solid #2563eb;
  background: rgba(37, 99, 235, 0.08);
  color: #1d4ed8;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:enabled {
    background: rgba(37, 99, 235, 0.16);
  }

  &:disabled {
    border-color: #cbd5f5;
    background: rgba(148, 163, 184, 0.12);
    color: #94a3b8;
    cursor: not-allowed;
  }
}

.radioOption {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  line-height: 20px;
  color: #334155;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 999px;
  transition: all 0.2s ease;
  user-select: none;

  &:hover {
    background: rgba(37, 99, 235, 0.08);
    color: #1d4ed8;
  }

  input {
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #94a3b8;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.2s ease;
    margin: 0;

    &:focus {
      border-color: #2563eb;
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.18);
      outline: none;
    }

    &:checked {
      border-color: #2563eb;
    }

    &:checked::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #2563eb;
    }
  }

  span {
    font-weight: 500;
    transition: color 0.2s ease;
  }
}

.activeOption {
  background: rgba(37, 99, 235, 0.12);
  color: #1d4ed8;

  span {
    color: inherit;
  }
}

.inputWrapper {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #475569;
  background: rgba(255, 255, 255, 0.8);
  padding: 6px 12px;
  border-radius: 10px;
  border: 1px solid #d9e3f4;
}

.labelText {
  font-weight: 500;
  color: #1e293b;
}

.numberInput {
  width: 140px;
  padding: 6px 10px;
  border: 1px solid #cbd5f5;
  border-radius: 8px;
  background: #f8faff;
  font-size: 14px;
  color: #0f172a;
  transition: all 0.2s ease;

  &:hover {
    border-color: #2563eb;
  }

  &:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.18);
    outline: none;
  }

  &::-webkit-inner-spin-button,
  &::-webkit-outer-spin-button {
    margin: 0;
  }
}

.viewer {
  flex: 1 1 auto;
  background: #fff;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: inset 0 1px 0 rgba(148, 163, 184, 0.2), 0 12px 24px rgba(15, 23, 42, 0.08);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow: hidden;
}

.viewerContent {
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  align-items: stretch;
  height: 100%;
}

.aiAnalysisThreeColumn {
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  align-items: stretch;
  height: 100%;
}

.sourceTextCardStack {
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 0;
}

.errorState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 40px;
  text-align: center;
  color: #1e293b;
  flex: 1 1 auto;
  min-height: 0;
  width: 100%;
}

.errorIcon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: rgba(239, 68, 68, 0.12);
  border: 1px solid rgba(239, 68, 68, 0.24);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: #ef4444;
}

.errorTitle {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.errorDescription {
  margin: 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.6;
  max-width: 360px;
}

.sourceTextCard {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  border: 1px solid rgba(37, 99, 235, 0.18);
  border-radius: 14px;
  background: linear-gradient(180deg, rgba(37, 99, 235, 0.04) 0%, rgba(37, 99, 235, 0.08) 100%);
  box-shadow: 0 8px 20px rgba(37, 99, 235, 0.12);
}

.sourceTextCardHeader {
  font-size: 15px;
  font-weight: 600;
  color: #1d4ed8;
}

.sourceTextCardMeta {
  display: block;
  margin-top: 6px;
  font-size: 12px;
  font-weight: 400;
  color: #94a3b8;
}

.sourceTextCardBody {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sourceTextResult {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 12px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.72);
  box-shadow: inset 0 1px 0 rgba(148, 163, 184, 0.16);
  border: 1px solid rgba(148, 163, 184, 0.24);
}

.sourceTextResultTitle {
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
  color: #0f172a;
}

.sourceTextResultTitleText {
  display: block;
}

.sourceTextResultMeta {
  font-size: 12px;
  font-weight: 400;
  color: #94a3b8;
  margin-top: 2px;
}

.sourceTextResultContent {
  margin: 0;
  font-size: 13px;
  line-height: 1.6;
  color: #1f2937;
  white-space: pre-wrap;
}

.viewerHighlight {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
  overflow: hidden;
}

.card {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  border: 1px solid rgba(37, 99, 235, 0.18);
  border-radius: 14px;
  background: linear-gradient(180deg, rgba(37, 99, 235, 0.04) 0%, rgba(37, 99, 235, 0.08) 100%);
  box-shadow: 0 8px 20px rgba(37, 99, 235, 0.12);
  flex: 1 1 auto;
  min-height: 0;
}

.cardHeader {
  line-height: 38px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1d4ed8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;

  h3 {
    margin: 0;
    font-size: 18px;
  }
}

.viewModeToggle {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.toggleOption {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  line-height: 20px;
  color: #64748b;
  cursor: pointer;
  padding: 4px 10px;
  border-radius: 6px;
  transition: all 0.2s ease;
  user-select: none;

  &:hover {
    background: rgba(37, 99, 235, 0.08);
    color: #1d4ed8;
  }

  input {
    appearance: none;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    border: 2px solid #94a3b8;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.2s ease;
    margin: 0;

    &:focus {
      border-color: #2563eb;
      box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.18);
      outline: none;
    }

    &:checked {
      border-color: #2563eb;
    }

    &:checked::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: #2563eb;
    }
  }

  span {
    font-weight: 500;
    transition: color 0.2s ease;
  }
}

.activeToggleOption {
  background: rgba(37, 99, 235, 0.12);
  color: #1d4ed8;

  span {
    color: inherit;
  }
}

.disabledToggleOption {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;

  &:hover {
    background: transparent;
    color: #64748b;
  }

  input {
    cursor: not-allowed;
    opacity: 0.5;
  }

  span {
    color: #94a3b8;
  }
}

.matchIndicator {
  flex: 0 0 auto;
  align-self: flex-start;
  padding: 4px 10px;
  border-radius: 999px;
  background: rgba(37, 99, 235, 0.12);
  color: #1e3a8a;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0.02em;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

.cardBody {
  padding: 12px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.72);
  box-shadow: inset 0 1px 0 rgba(148, 163, 184, 0.16);
  border: 1px solid rgba(148, 163, 184, 0.24);
  color: #1f2937;
  font-size: 13px;
  line-height: 1.6;
  white-space: pre-wrap;
  flex: 1 1 auto;
  overflow-y: auto;
  min-height: 0;
}

.analyzeButton {
  padding: 8px 20px;
  border-radius: 8px;
  border: 1px solid #2563eb;
  background: #2563eb;
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:enabled {
    background: #1d4ed8;
    border-color: #1d4ed8;
  }

  &:disabled {
    border-color: #cbd5f5;
    background: #94a3b8;
    color: #fff;
    cursor: not-allowed;
    opacity: 0.6;
  }
}

.aiAnalysisContainer {
  flex: 1 1 auto;
  overflow-y: auto;
  min-height: 0;
}

.aiAnalysisPanel {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.aiAnalysisHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: rgba(37, 99, 235, 0.08);
  border-radius: 12px;
  border: 1px solid rgba(37, 99, 235, 0.18);
}

.aiAnalysisTitle {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1d4ed8;
}

.aiAnalysisCount {
  font-size: 13px;
  color: #64748b;
  font-weight: 500;
}

.aiAnalysisList {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1 1 auto;
  overflow-y: auto;
  min-height: 0;
}

.aiAnalysisCard {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  background: #fff;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(15, 23, 42, 0.06);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(15, 23, 42, 0.1);
    border-color: #cbd5f5;
  }
}

.aiAnalysisCardHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e2e8f0;
}

.cardIndex {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: rgba(37, 99, 235, 0.12);
  color: #1d4ed8;
  font-size: 13px;
  font-weight: 600;
}

.sourceQuestion {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.aiAnalysisCardBody {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.outputContent {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.contentLabel {
  font-size: 13px;
  font-weight: 600;
  color: #475569;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.contentText {
  padding: 12px;
  border-radius: 8px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  font-size: 14px;
  line-height: 1.7;
  color: #1e293b;
  white-space: pre-wrap;
}

.sourceInfo {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  border-radius: 8px;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;

  &:hover {
    background: #e2e8f0;
    border-color: #cbd5f5;
  }
}

.sourceItem {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 13px;
  line-height: 1.6;
}

.sourceLabel {
  font-weight: 600;
  color: #64748b;
  white-space: nowrap;
}

.sourceValue {
  color: #1e293b;
  flex: 1;
}

.aiAnalysisNoData {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background: #fef3c7;
  border: 1px solid #fde68a;
  color: #92400e;
}

.noDataHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
}

.noDataIcon {
  font-size: 18px;
}

.noDataTitle {
  color: #92400e;
}

.noDataQuestion {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 13px;
  line-height: 1.6;
  padding-top: 8px;
  border-top: 1px solid rgba(146, 64, 14, 0.2);

  .sourceLabel {
    color: #92400e;
    opacity: 0.8;
  }

  .sourceValue {
    color: #92400e;
  }
}

.aiAnalysisLoading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 60px 20px;
  color: #64748b;
  font-size: 14px;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top-color: #2563eb;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.aiAnalysisEmpty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 60px 20px;
  color: #94a3b8;
  font-size: 14px;
}

.emptyIcon {
  font-size: 48px;
  opacity: 0.5;
}

.sourceInfoHighlight {
  animation: highlightPulse 2s ease-in-out;
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

@keyframes highlightPulse {
  0% {
    outline-color: #2563eb;
    outline-width: 2px;
  }

  50% {
    outline-color: #60a5fa;
    outline-width: 4px;
  }

  100% {
    outline-color: transparent;
    outline-width: 0;
  }
}