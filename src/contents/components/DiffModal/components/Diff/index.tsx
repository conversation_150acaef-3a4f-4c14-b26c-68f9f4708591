import { useCallback, useMemo, useRef } from 'react';

import type { TraceData } from './types';

import { useDiffState } from './hooks/useDiffState';
import { useViewModeState } from './hooks/useViewModeState';
import { useActiveOccurrence } from './hooks/useActiveOccurrence';
import { useHighlightRanges } from './hooks/useHighlightRanges';
import { useMatchSelection } from './hooks/useMatchSelection';
import { useSourceTextRender } from './hooks/useSourceTextRender';
import { useDiffHandlers } from './hooks/useDiffHandlers';
import { buildOccurrenceSummary } from './utils/occurrence';
import { ScenarioContext } from './utils/scenarioContext';
import type { PlainTextViewerHandle } from './components/PlainTextViewer';
import { DiffView } from './components/DiffView';
import type { MatchMode } from './utils/scenario';

export interface DiffProps {
  data: TraceData | undefined;
}

/**
 * Diff 主组件，汇总三类文本的展示，同时负责高亮模式、自由选区以及左右联动逻辑。
 * 三类文本定义：
 * 1. 润色出话：左侧取自 output 字段，代表润色后的输出文本。
 * 2. 召回信息：右侧召回信息取自 currentQuery 字段，代表召回查询信息。
 * 3. 检索结果：右侧检索结果取自 searchDataList 字段，代表检索到的结果列表。
 * 页面构成：
 * 1. 工具栏：切换高亮模式、阈值、自由选择子模式。
 * 2. 右侧区域：根据 rightViewMode 显示检索结果或召回信息。
 * 3. 左侧区域：通过 `PlainTextViewer` 呈现润色出话，并与右侧文本实现交互同步。
 */
const Diff = ({ data }: DiffProps) => {
  // 状态管理
  const state = useDiffState(data);
  const {
    minLength,
    mode,
    freeMode,
    freeGroups,
    selectedMatchId,
    loading,
    error,
    composedSourceText,
    sourceTextViewBlocks,
    composedPolishedOutputText,
    recallInfoText,
    setMinLength,
    setMode,
    setFreeMode,
    setFreeGroups,
    setSelectedMatchId,
  } = state;

  // 管理右侧视图模式的状态（必须在 sourceTextViewBlocks 计算之后）
  const viewModeState = useViewModeState({
    sourceTextBlocksCount: sourceTextViewBlocks.length,
    recallInfoText: recallInfoText ?? '',
  });

  // Active occurrence 管理
  const occurrence = useActiveOccurrence();
  const {
    sourceTextActiveOccurrence,
    polishedOutputActiveOccurrence,
    pendingAutoSelectRef,
    sourceTextHighlightRefs,
    sourceTextCycleIndexRef,
    polishedOutputCycleIndexRef,
    lastInteractionSourceRef,
    setSourceTextActiveOccurrence,
    setPolishedOutputActiveOccurrence,
  } = occurrence;

  // Refs
  const polishedOutputViewerRef = useRef<PlainTextViewerHandle | null>(null);
  const recallInfoViewerRef = useRef<PlainTextViewerHandle | null>(null);

  // 高亮范围计算
  const highlightRangesResult = useHighlightRanges({
    mode,
    minLength,
    freeGroups,
    rightViewMode: viewModeState.rightViewMode,
    composedSourceText,
    composedPolishedOutputText,
    recallInfoText,
    setFreeGroups,
  });

  const { sourceTextRanges, polishedOutputRanges, recallInfoHighlightRanges } =
    highlightRangesResult;

  // 匹配选择逻辑
  const matchSelection = useMatchSelection({
    selectedMatchId,
    setSelectedMatchId,
    sourceTextActiveOccurrence,
    polishedOutputActiveOccurrence,
    sourceTextHighlightRanges: sourceTextRanges,
    polishedOutputHighlightRanges: polishedOutputRanges,
    sourceTextHighlightRefs,
    polishedOutputViewerRef,
    recallInfoViewerRef,
    sourceTextCycleIndexRef,
    polishedOutputCycleIndexRef,
    lastInteractionSourceRef,
    setSourceTextActiveOccurrence,
    setPolishedOutputActiveOccurrence,
    rightViewMode: viewModeState.rightViewMode,
    mode: mode as import('./utils/scenario').MatchMode,
  });

  const { handleMatchSelect } = matchSelection;

  // 检索结果渲染
  const sourceTextRender = useSourceTextRender({
    sourceTextHighlightRanges: sourceTextRanges,
    mode,
    selectedMatchId,
    sourceTextActiveOccurrence,
    registerSourceTextHighlightRef: useCallback(
      (
        matchId: string | undefined,
        element: HTMLElement | null,
        occurrenceIndex = 0
      ) => {
        if (!matchId) {
          return;
        }
        if (element) {
          if (!sourceTextHighlightRefs.current[matchId]) {
            sourceTextHighlightRefs.current[matchId] = [];
          }
          sourceTextHighlightRefs.current[matchId][occurrenceIndex] = element;
          return;
        }
        const list = sourceTextHighlightRefs.current[matchId];
        if (!list) {
          return;
        }
        const filtered = list.filter(
          (node) => node?.isConnected
        ) as HTMLElement[];
        if (filtered.length) {
          sourceTextHighlightRefs.current[matchId] = filtered;
        } else {
          delete sourceTextHighlightRefs.current[matchId];
        }
      },
      [sourceTextHighlightRefs]
    ),
    handleMatchSelect,
  });

  const { renderSegments } = sourceTextRender;

  // 事件处理
  const handlers = useDiffHandlers({
    mode,
    freeMode,
    freeGroups,
    composedSourceText,
    composedPolishedOutputText,
    recallInfoText,
    rightViewMode: viewModeState.rightViewMode,
    setMode,
    setMinLength,
    setFreeMode,
    setFreeGroups,
    setSelectedMatchId,
    setSourceTextActiveOccurrence,
    setPolishedOutputActiveOccurrence,
    pendingAutoSelectRef,
    loading,
    sourceTextHighlightRanges: sourceTextRanges,
    polishedOutputHighlightRanges: polishedOutputRanges,
    handleMatchSelect,
  });

  // 创建场景上下文
  const scenarioContext = useMemo(
    () => new ScenarioContext(mode as MatchMode, viewModeState.rightViewMode),
    [mode, viewModeState.rightViewMode]
  );

  // 计算 occurrence summary - 根据场景计算对应的 summary
  // 检索结果场景的 occurrence summary
  const sourceTextOccurrenceSummary = useMemo(() => {
    if (viewModeState.rightViewMode !== 'search-result') {
      return undefined;
    }
    // 检索结果文本（来自 searchDataList）
    const searchResultText = scenarioContext.getSourceText({
      composedSourceText,
      recallInfoText,
    });
    return buildOccurrenceSummary(
      sourceTextActiveOccurrence,
      sourceTextRanges,
      searchResultText
    );
  }, [
    scenarioContext,
    sourceTextActiveOccurrence,
    sourceTextRanges,
    viewModeState.rightViewMode,
    composedSourceText,
    recallInfoText,
  ]);

  // 召回信息场景的 occurrence summary
  const recallInfoOccurrenceSummary = useMemo(() => {
    if (viewModeState.rightViewMode !== 'recall-info') {
      return undefined;
    }
    // 召回信息文本（来自 currentQuery）
    const recallText = scenarioContext.getSourceText({
      composedSourceText,
      recallInfoText,
    });
    return buildOccurrenceSummary(
      sourceTextActiveOccurrence,
      recallInfoHighlightRanges.sourceTextRanges,
      recallText
    );
  }, [
    scenarioContext,
    sourceTextActiveOccurrence,
    recallInfoHighlightRanges.sourceTextRanges,
    recallInfoText,
    composedSourceText,
    viewModeState.rightViewMode,
  ]);

  const hasFreeSelections = freeGroups.length > 0;
  const enablePolishedOutputSelection = mode === 'free' && freeMode === 'pick';

  // 判断数据是否为空：检索结果列表为空且润色出话文本为空
  const isEmpty = useMemo(
    () =>
      sourceTextViewBlocks.length === 0 &&
      !composedPolishedOutputText &&
      !recallInfoText,
    [sourceTextViewBlocks.length, composedPolishedOutputText, recallInfoText]
  );

  return (
    <DiffView
      mode={mode}
      loading={loading}
      error={!!error}
      minLength={minLength}
      freeMode={freeMode}
      hasFreeSelections={hasFreeSelections}
      rightViewMode={viewModeState.rightViewMode}
      sourceTextViewBlocks={sourceTextViewBlocks}
      composedPolishedOutputText={composedPolishedOutputText}
      recallInfoText={recallInfoText}
      polishedOutputHighlightRanges={polishedOutputRanges}
      recallInfoHighlightRanges={recallInfoHighlightRanges}
      selectedMatchId={selectedMatchId}
      sourceTextActiveOccurrence={sourceTextActiveOccurrence}
      polishedOutputActiveOccurrence={polishedOutputActiveOccurrence}
      sourceTextOccurrenceSummary={sourceTextOccurrenceSummary}
      recallInfoOccurrenceSummary={recallInfoOccurrenceSummary}
      enablePolishedOutputSelection={enablePolishedOutputSelection}
      polishedOutputViewerRef={polishedOutputViewerRef}
      recallInfoViewerRef={recallInfoViewerRef}
      renderSegments={renderSegments}
      onModeChange={handlers.handleModeChange}
      onMinLengthChange={handlers.handleMinLengthChange}
      onFreeModeChange={handlers.handleFreeModeChange}
      onFreeClear={handlers.handleFreeClear}
      onPolishedOutputSelection={handlers.handlePolishedOutputSelection}
      onMatchSelect={handleMatchSelect}
      onRightViewModeChange={viewModeState.setRightViewMode}
      isEmpty={isEmpty}
      viewModeState={viewModeState}
    />
  );
};

export default Diff;
