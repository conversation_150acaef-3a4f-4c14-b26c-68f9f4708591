import React, { useCallback, useMemo } from 'react';
import type { KeyboardEvent } from 'react';
import type { HighlightMode, HighlightRange, TextRange } from '../types';
import type { ActiveOccurrence } from '../components/PlainTextViewer';
import { buildSegments, lightenHighlightColor } from '../utils';
import { getOccurrenceKey, buildSourceTextOccurrenceIndexMap } from '../utils/occurrence';

interface UseSourceTextRenderParams {
  sourceTextHighlightRanges: HighlightRange[];
  mode: HighlightMode;
  selectedMatchId: string | undefined;
  sourceTextActiveOccurrence: ActiveOccurrence | undefined;
  registerSourceTextHighlightRef: (
    matchId: string | undefined,
    element: HTMLElement | null,
    occurrenceIndex?: number
  ) => void;
  handleMatchSelect: (
    matchId: string,
    source: 'sourceText' | 'polishedOutput',
    occurrenceIndex?: number
  ) => void;
}

/**
 * 处理原文渲染相关的逻辑
 */
export const useSourceTextRender = (params: UseSourceTextRenderParams) => {
  const {
    sourceTextHighlightRanges,
    mode,
    selectedMatchId,
    sourceTextActiveOccurrence,
    registerSourceTextHighlightRef,
    handleMatchSelect,
  } = params;

  const sourceTextOccurrenceIndexMap = useMemo(() => {
    return buildSourceTextOccurrenceIndexMap(sourceTextHighlightRanges);
  }, [sourceTextHighlightRanges]);

  /**
   * 根据检索结果卡片（标题/内容）的局部范围，裁剪出实际需要渲染的高亮区间。
   * 通过转换为相对位置，保证 `buildSegments` 渲染时不会越界。
   */
  const getLocalRanges = useCallback(
    (range?: TextRange): (HighlightRange & { occurrenceIndex?: number })[] => {
      if (!range) {
        return [];
      }
      const { start, end } = range;
      if (start === end) {
        return [];
      }
      return sourceTextHighlightRanges
        .filter((highlight) => highlight.start < end && highlight.end > start)
        .map((highlight) => {
          const globalStart = highlight.start;
          const globalEnd = highlight.end;
          const occurrenceIndex =
            sourceTextOccurrenceIndexMap.get(
              getOccurrenceKey(highlight.matchId, globalStart, globalEnd)
            ) ?? 0;
          return {
            ...highlight,
            start: Math.max(globalStart, start) - start,
            end: Math.min(globalEnd, end) - start,
            occurrenceIndex,
          };
        });
    },
    [sourceTextHighlightRanges, sourceTextOccurrenceIndexMap]
  );

  /**
   * 按照传入文本拆分出普通片段与高亮片段，渲染为 `span/mark`。
   * 该函数注入到 `ArticleCardViewer` 中，保证局部渲染与主组件状态保持一致。
   */
  const renderSegments = useMemo(() => {
    return (text: string, range: TextRange | undefined, keyPrefix: string) => {
      if (!text) {
        return null;
      }
      const localRanges = getLocalRanges(range);
      if (!localRanges.length) {
        return text;
      }
      const segments = buildSegments(text, localRanges, keyPrefix);
      const isNumbersMode = mode === 'numbers';
      const isFreeMode = mode === 'free';

      return segments.map((segment) => {
        if (!segment.text) {
          return null;
        }
        if (!segment.highlight || !segment.matchId) {
          return (
            <span key={segment.id} style={{ display: 'inline' }}>
              {segment.text}
            </span>
          );
        }
        const matchId = segment.matchId;
        const occurrenceIndex = segment.occurrenceIndex ?? 0;
        const isActiveMatch = !isNumbersMode || selectedMatchId === matchId;
        const isFocusedOccurrence =
          sourceTextActiveOccurrence?.matchId === matchId &&
          sourceTextActiveOccurrence.occurrenceIndex === occurrenceIndex;
        const commonProps = {
          ref: (element: HTMLElement | null) =>
            registerSourceTextHighlightRef(matchId, element, occurrenceIndex),
          'data-match-id': matchId,
          'data-match-index': occurrenceIndex,
          tabIndex: 0,
          onClick: () => handleMatchSelect(matchId, 'sourceText', occurrenceIndex),
          onKeyDown: (event: KeyboardEvent<HTMLElement>) => {
            if (event.key === 'Enter' || event.key === ' ') {
              event.preventDefault();
              handleMatchSelect(matchId, 'sourceText', occurrenceIndex);
            }
          },
        };

        const outlineStyle = isFocusedOccurrence
          ? '2px solid rgba(0, 0, 0, 0.4)'
          : 'none';

        if (!isActiveMatch) {
          return (
            <span
              key={segment.id}
              {...commonProps}
              style={{
                cursor: 'pointer',
                outline: outlineStyle,
              }}
            >
              {segment.text}
            </span>
          );
        }

        return (
          <mark
            {...commonProps}
            style={{
              backgroundColor:
                isNumbersMode || isFreeMode
                  ? lightenHighlightColor(segment.color)
                  : segment.color,
              display: 'inline',
              cursor: 'pointer',
              outline: outlineStyle,
              transition: 'outline 0.2s ease',
            }}
          >
            {segment.text}
          </mark>
        );
      });
    };
  }, [
    getLocalRanges,
    handleMatchSelect,
    mode,
    sourceTextActiveOccurrence,
    registerSourceTextHighlightRef,
    selectedMatchId,
  ]);

  return {
    renderSegments,
  };
};

