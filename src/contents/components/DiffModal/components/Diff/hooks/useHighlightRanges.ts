import { useMemo, useEffect, useRef } from 'react';
import type { HighlightMode } from '../types';
import type { SourceMode, MatchMode } from '../utils/scenario';
import {
  calculateScenarioHighlightRanges,
  getScenarioConfig,
} from '../utils/scenario';
import { findAllOccurrences } from '../utils/text';
import type { HighlightRange } from '../types';

interface UseHighlightRangesParams {
  mode: HighlightMode;
  minLength: number;
  freeGroups: import('../types').SelectionGroup[];
  rightViewMode: SourceMode;
  composedSourceText: string;
  composedPolishedOutputText: string;
  recallInfoText: string;
  setFreeGroups: (groups: import('../types').SelectionGroup[]) => void;
}

/**
 * 计算高亮范围的核心逻辑
 * 使用策略模式处理6种场景（3种匹配逻辑 × 2种来源选择）
 */
export const useHighlightRanges = (params: UseHighlightRangesParams) => {
  const {
    mode,
    minLength,
    freeGroups,
    rightViewMode,
    composedSourceText,
    composedPolishedOutputText,
    recallInfoText,
    setFreeGroups,
  } = params;

  const prevRightViewModeRef = useRef<SourceMode>('recall-info');

  /**
   * 当切换视图模式时，重新计算自由选择的高亮范围
   * 因为 sourceTextHighlightRanges 需要基于当前视图模式对应的右侧文本（检索结果或召回信息）重新计算
   */
  useEffect(() => {
    // 如果视图模式没有改变，或者不是自由选择模式，或者没有自由选择的分组，则不需要重新计算
    if (
      prevRightViewModeRef.current === rightViewMode ||
      mode !== 'free' ||
      freeGroups.length === 0
    ) {
      prevRightViewModeRef.current = rightViewMode;
      return;
    }

    // 使用场景配置获取当前场景对应的右侧文本（检索结果或召回信息）
    const config = getScenarioConfig(mode as MatchMode, rightViewMode);
    const currentSourceText = config.getSourceText({
      composedSourceText,
      recallInfoText,
    });

    // 重新计算所有 freeGroups 中的 sourceTextRanges
    const updatedGroups = freeGroups.map((group) => {
      const sourceTextPositions = findAllOccurrences(currentSourceText, group.text);
      const sourceTextRanges: HighlightRange[] = sourceTextPositions.map(
        (position) => ({
          start: position,
          end: position + group.text.length,
          color: group.color,
          matchId: group.matchId,
        })
      );

      return {
        ...group,
        sourceTextRanges,
      };
    });

    setFreeGroups(updatedGroups);
    prevRightViewModeRef.current = rightViewMode;
  }, [
    rightViewMode,
    mode,
    recallInfoText,
    composedSourceText,
    freeGroups,
    setFreeGroups,
  ]);

  /**
   * 根据当前场景计算高亮范围（6种场景统一处理）
   */
  const highlightRanges = useMemo(() => {
    return calculateScenarioHighlightRanges(
      mode as MatchMode,
      rightViewMode,
      {
        composedSourceText,
        composedPolishedOutputText,
        recallInfoText,
        minLength,
        freeGroups,
      }
    );
  }, [
    mode,
    rightViewMode,
    composedSourceText,
    composedPolishedOutputText,
    recallInfoText,
    minLength,
    freeGroups,
  ]);

  /**
   * 召回信息场景的高亮范围（用于特殊场景，如 occurrence summary 计算）
   */
  const recallInfoHighlightRanges = useMemo(() => {
    if (rightViewMode !== 'recall-info' || !recallInfoText) {
      return { sourceTextRanges: [], polishedOutputRanges: [] };
    }
    return calculateScenarioHighlightRanges(
      mode as MatchMode,
      'recall-info',
      {
        composedSourceText,
        composedPolishedOutputText,
        recallInfoText,
        minLength,
        freeGroups,
      }
    );
  }, [
    mode,
    rightViewMode,
    recallInfoText,
    composedSourceText,
    composedPolishedOutputText,
    minLength,
    freeGroups,
  ]);

  /**
   * 对于自由选择模式，检查哪些 groups 的所有 ranges 都被过滤掉了。
   * 如果某个 group 的所有 ranges（sourceTextRanges 和 polishedOutputRanges）都被过滤掉了，
   * 则视为未找到，从 freeGroups 中移除它。
   */
  useEffect(() => {
    if (mode !== 'free' || freeGroups.length === 0) {
      return;
    }

    // 收集过滤后的所有 matchId
    const filteredMatchIds = new Set<string>();
    highlightRanges.sourceTextRanges.forEach((range) => {
      filteredMatchIds.add(range.matchId);
    });
    highlightRanges.polishedOutputRanges.forEach((range) => {
      filteredMatchIds.add(range.matchId);
    });

    // 检查哪些 groups 的所有 ranges 都被过滤掉了
    const validGroups = freeGroups.filter((group) => {
      // 如果某个 group 的 matchId 在过滤后的 ranges 中，说明至少有一个 range 被保留了
      return filteredMatchIds.has(group.matchId);
    });

    // 如果有 groups 被过滤掉了，更新 freeGroups
    if (validGroups.length !== freeGroups.length) {
      setFreeGroups(validGroups);
    }
  }, [
    mode,
    freeGroups,
    highlightRanges.sourceTextRanges,
    highlightRanges.polishedOutputRanges,
    setFreeGroups,
  ]);

  return {
    sourceTextRanges: highlightRanges.sourceTextRanges,
    polishedOutputRanges: highlightRanges.polishedOutputRanges,
    recallInfoHighlightRanges,
  };
};
