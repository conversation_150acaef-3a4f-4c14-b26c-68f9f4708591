import { useCallback, useEffect } from 'react';
import type { HighlightMode, FreeSelectionMode } from '../types';
import type { FreeSelectionPayload } from '../types';
import type { SourceMode } from '../utils/scenario';
import { getScenarioConfig } from '../utils/scenario';
import { showWarning } from '../../Message';
import { tryAddSelectionGroup } from '../utils/free';
import type { ActiveOccurrence } from '../components/PlainTextViewer';

interface UseDiffHandlersParams {
  mode: HighlightMode;
  freeMode: FreeSelectionMode;
  freeGroups: import('../types').SelectionGroup[];
  composedSourceText: string;
  composedPolishedOutputText: string;
  recallInfoText: string;
  rightViewMode: SourceMode;
  setMode: (mode: HighlightMode) => void;
  setMinLength: (length: number) => void;
  setFreeMode: (mode: FreeSelectionMode) => void;
  setFreeGroups: (groups: import('../types').SelectionGroup[]) => void;
  setSelectedMatchId: (id: string | undefined) => void;
  setSourceTextActiveOccurrence: (occurrence: ActiveOccurrence | undefined) => void;
  setPolishedOutputActiveOccurrence: (occurrence: ActiveOccurrence | undefined) => void;
  pendingAutoSelectRef: React.MutableRefObject<ActiveOccurrence | null>;
  loading: boolean;
  sourceTextHighlightRanges: import('../types').HighlightRange[];
  polishedOutputHighlightRanges: import('../types').HighlightRange[];
  handleMatchSelect: (
    matchId: string,
    source: 'sourceText' | 'polishedOutput',
    occurrenceIndex?: number
  ) => void;
}

/**
 * 处理所有事件处理函数
 */
export const useDiffHandlers = (params: UseDiffHandlersParams) => {
  const {
    mode,
    freeMode,
    freeGroups,
    composedSourceText,
    composedPolishedOutputText,
    recallInfoText,
    rightViewMode,
    setMode,
    setMinLength,
    setFreeMode,
    setFreeGroups,
    setSelectedMatchId,
    setSourceTextActiveOccurrence,
    setPolishedOutputActiveOccurrence,
    pendingAutoSelectRef,
    loading,
    sourceTextHighlightRanges,
    polishedOutputHighlightRanges,
    handleMatchSelect,
  } = params;

  /** 处理阈值输入框变化 */
  const handleMinLengthChange = useCallback(
    (value: number) => {
      setMinLength(Number.isNaN(value) ? 0 : value);
    },
    [setMinLength]
  );

  /** 切换高亮模式 */
  const handleModeChange = useCallback(
    (nextMode: HighlightMode) => {
      setMode(nextMode);
      setSelectedMatchId(undefined);
      setSourceTextActiveOccurrence(undefined);
      setPolishedOutputActiveOccurrence(undefined);
    },
    [
      setMode,
      setSelectedMatchId,
      setSourceTextActiveOccurrence,
      setPolishedOutputActiveOccurrence,
    ]
  );

  /** 切换自由选择模式下的子模式 */
  const handleFreeModeChange = useCallback(
    (nextMode: FreeSelectionMode) => {
      setFreeMode(nextMode);
    },
    [setFreeMode]
  );

  /** 清除自由选择模式下的所有高亮与状态联动信息。 */
  const handleFreeClear = useCallback(() => {
    setFreeGroups([]);
    setSelectedMatchId(undefined);
    setSourceTextActiveOccurrence(undefined);
    setPolishedOutputActiveOccurrence(undefined);
    window.getSelection()?.removeAllRanges();
  }, [
    setFreeGroups,
    setSelectedMatchId,
    setSourceTextActiveOccurrence,
    setPolishedOutputActiveOccurrence,
  ]);

  /** 当数据加载完成后，重置自由选择模式下的所有临时高亮。 */
  useEffect(() => {
    if (!loading) {
      setFreeGroups([]);
    }
  }, [loading, setFreeGroups]);

  /**
   * 处理目标区域的自由选择回调：
   * - 在 pick 模式下尝试新增自由高亮分组。
   * - 成功后重置浏览器自带的 selection，避免残留高亮。
   * - 在召回信息模式下，使用召回信息文本（currentQuery）进行匹配。
   * - 在检索结果模式下，使用检索结果文本（searchDataList）进行匹配。
   */
  const handlePolishedOutputSelection = useCallback(
    (selection?: FreeSelectionPayload) => {
      const enablePolishedOutputSelection = mode === 'free' && freeMode === 'pick';
      if (!enablePolishedOutputSelection) {
        return;
      }
      // 使用场景配置获取当前场景对应的右侧文本（检索结果或召回信息）
      const config = getScenarioConfig(
        mode as import('../utils/scenario').MatchMode,
        rightViewMode
      );
      const sourceText = config.getSourceText({
        composedSourceText,
        recallInfoText,
      });
      const result = tryAddSelectionGroup({
        selection,
        groups: freeGroups,
        sourceText,
        polishedOutputText: composedPolishedOutputText,
      });
      if (result.type === 'warning' && result.message) {
        showWarning(result.message);
      }
      if (result.type === 'success') {
        setFreeGroups(result.groups);
        /**
         * 在自由模式下，匹配成功后默认触发一次目标侧点击，
         * 以便立即同步 occurrence 状态与左右联动。
         */
        const addedGroup = result.groups[result.groups.length - 1];
        if (addedGroup?.matchId) {
          const polishedOutputRanges = addedGroup.polishedOutputRanges ?? [];
          const anchorStart = addedGroup.anchor.start;
          const anchorEnd = addedGroup.anchor.end;
          const matchedIndex = polishedOutputRanges.findIndex(
            (range) => range.start === anchorStart && range.end === anchorEnd
          );
          pendingAutoSelectRef.current = {
            matchId: addedGroup.matchId,
            occurrenceIndex: matchedIndex >= 0 ? matchedIndex : 0,
          };
        }
      }
      if (selection) {
        window.getSelection()?.removeAllRanges();
      }
    },
    [
      mode,
      freeMode,
      freeGroups,
      rightViewMode,
      recallInfoText,
      composedSourceText,
      composedPolishedOutputText,
      setFreeGroups,
      pendingAutoSelectRef,
    ]
  );


  /**
   * 当自由模式新增高亮后，等待高亮数据同步完成再执行的自动点击任务。
   */
  useEffect(() => {
    const pending = pendingAutoSelectRef.current;
    if (!pending) {
      return;
    }
    const hasSourceTextMatch = sourceTextHighlightRanges.some(
      (range) => range.matchId === pending.matchId
    );
    const hasPolishedOutputMatch = polishedOutputHighlightRanges.some(
      (range) => range.matchId === pending.matchId
    );
    if (!hasSourceTextMatch && !hasPolishedOutputMatch) {
      return;
    }
    pendingAutoSelectRef.current = null;
    handleMatchSelect(pending.matchId, 'polishedOutput', pending.occurrenceIndex);
  }, [handleMatchSelect, sourceTextHighlightRanges, polishedOutputHighlightRanges, pendingAutoSelectRef]);

  return {
    handleMinLengthChange,
    handleModeChange,
    handleFreeModeChange,
    handleFreeClear,
    handlePolishedOutputSelection,
  };
};

