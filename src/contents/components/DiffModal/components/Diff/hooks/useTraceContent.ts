import { useMemo } from 'react';

import { mapTraceData } from '../utils/sourceText';
import type { TraceData } from '../types';

export type TraceSourceTextBlock = {
  title: string;
  resultList: { title?: string; content: string; pubTime?: string }[];
};

const ZERO_STATE: TraceSourceTextBlock[] = [];

/**
 * 从 Trace 数据中提取检索结果分块、润色出话文本和召回信息文本。
 * 返回检索结果分块（来自 searchDataList）、润色出话文本（来自 output）、召回信息文本（来自 currentQuery）。
 */
const useTraceContent = (data: TraceData | undefined) => {
  const result = useMemo(() => {
    const mappedData = mapTraceData(data);
    const hasSourceText = mappedData.sourceTextList && mappedData.sourceTextList.length;
    return {
      sourceTextBlocks: hasSourceText ? mappedData.sourceTextList : ZERO_STATE,
      polishedOutputContent: mappedData.polishedOutputText ?? '',
      recallInfoText: mappedData.recallInfoText ?? '',
    };
  }, [data]);

  return {
    sourceTextBlocks: result.sourceTextBlocks,
    polishedOutputContent: result.polishedOutputContent,
    recallInfoText: result.recallInfoText,
    loading: false,
    error: undefined,
  };
};

export default useTraceContent;
