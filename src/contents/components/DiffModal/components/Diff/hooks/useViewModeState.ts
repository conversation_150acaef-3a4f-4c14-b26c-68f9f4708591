import { useEffect, useMemo, useRef, useState } from 'react';

type RightViewMode = 'search-result' | 'recall-info';

interface UseViewModeStateParams {
  sourceTextBlocksCount: number;
  recallInfoText: string;
}

interface ViewModeState {
  /** 当前视图模式 */
  rightViewMode: RightViewMode;
  /** 设置视图模式的函数 */
  setRightViewMode: (mode: RightViewMode) => void;
  /** 检索结果是否为空 */
  isSourceTextEmpty: boolean;
  /** 召回信息是否为空 */
  isRecallInfoEmpty: boolean;
  /** 两者是否都为空 */
  bothEmpty: boolean;
  /** 召回信息选项是否禁用 */
  isRecallInfoDisabled: boolean;
  /** 检索结果选项是否禁用 */
  isSearchResultDisabled: boolean;
  /** 是否应该选中召回信息 */
  shouldCheckRecallInfo: (currentMode: RightViewMode) => boolean;
  /** 是否应该选中检索结果 */
  shouldCheckSearchResult: (currentMode: RightViewMode) => boolean;
}

/**
 * 管理右侧视图模式的状态和逻辑
 * - 根据数据可用性自动选择合适的默认模式
 * - 当数据变化时自动切换模式（避免选中空数据）
 * - 提供禁用状态和选中状态的判断方法
 */
export const useViewModeState = ({
  sourceTextBlocksCount,
  recallInfoText,
}: UseViewModeStateParams): ViewModeState => {
  const [rightViewMode, setRightViewMode] = useState<RightViewMode>('recall-info');
  const hasInitializedModeRef = useRef(false);

  // 计算各种空状态
  const state = useMemo(() => {
    const isSourceTextEmpty = sourceTextBlocksCount === 0;
    const isRecallInfoEmpty = !recallInfoText || recallInfoText.trim().length === 0;
    const bothEmpty = isSourceTextEmpty && isRecallInfoEmpty;
    const hasSourceText = !isSourceTextEmpty;
    const hasRecallInfo = !isRecallInfoEmpty;

    // 判断选项的禁用状态
    const isRecallInfoDisabled = bothEmpty || (isRecallInfoEmpty && hasSourceText);
    const isSearchResultDisabled = bothEmpty || isSourceTextEmpty;

    // 判断是否应该选中某个选项（当两个都为空时，都不选中）
    const shouldCheckRecallInfo = (currentMode: RightViewMode) =>
      currentMode === 'recall-info' && !bothEmpty;
    const shouldCheckSearchResult = (currentMode: RightViewMode) =>
      currentMode === 'search-result' && !bothEmpty;

    return {
      isSourceTextEmpty,
      isRecallInfoEmpty,
      bothEmpty,
      isRecallInfoDisabled,
      isSearchResultDisabled,
      shouldCheckRecallInfo,
      shouldCheckSearchResult,
      hasSourceText,
      hasRecallInfo,
    };
  }, [sourceTextBlocksCount, recallInfoText]);

  // 初始化默认模式（只在首次加载时）
  useEffect(() => {
    if (hasInitializedModeRef.current) {
      return;
    }

    // 如果检索结果有数据且召回信息为空，默认为检索结果
    if (state.hasSourceText && !state.hasRecallInfo) {
      setRightViewMode('search-result');
      hasInitializedModeRef.current = true;
    } else {
      // 其他情况保持默认的 recall-info
      hasInitializedModeRef.current = true;
    }
  }, [state.hasSourceText, state.hasRecallInfo]);

  // 自动切换逻辑：当选中了没有数据的模式时，自动切换到有数据的模式
  useEffect(() => {
    // 如果两者都为空，不进行切换
    if (state.bothEmpty) {
      return;
    }

    // 如果选中了检索结果模式，但检索结果为空，切换到召回信息（如果召回信息有数据）
    if (rightViewMode === 'search-result' && state.isSourceTextEmpty && state.hasRecallInfo) {
      setRightViewMode('recall-info');
      return;
    }

    // 如果选中了召回信息模式，但召回信息为空，切换到检索结果（如果检索结果有数据）
    if (rightViewMode === 'recall-info' && state.isRecallInfoEmpty && state.hasSourceText) {
      setRightViewMode('search-result');
      return;
    }
  }, [rightViewMode, state]);

  return {
    rightViewMode,
    setRightViewMode,
    ...state,
  };
};

