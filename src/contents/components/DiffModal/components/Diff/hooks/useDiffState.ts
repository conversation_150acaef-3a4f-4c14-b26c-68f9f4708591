import { useState, useMemo } from 'react';
import type { HighlightMode, FreeSelectionMode } from '../types';
import useTraceContent from './useTraceContent';
import { composeSourceTextContent } from '../utils/sourceText';
import type { TraceData } from '../types';

/**
 * 管理 Diff 组件的所有状态
 */
export const useDiffState = (data: TraceData | undefined) => {
  /** 长文本高亮的最小匹配长度，默认 12 个字符，且不小于 2 */
  const [minLength, setMinLength] = useState<number>(12);
  /** 当前所处的高亮模式（数字模式 / 文本模式） */
  const [mode, setMode] = useState<HighlightMode>('text');
  /** 自由选择模式下的子模式（点选 / 阅读） */
  const [freeMode, setFreeMode] = useState<FreeSelectionMode>('pick');
  /** 自由选择模式下的高亮分组 */
  const [freeGroups, setFreeGroups] = useState<
    import('../types').SelectionGroup[]
  >([]);
  /** 当前选中的高亮分组 id，用于左右联动 */
  const [selectedMatchId, setSelectedMatchId] = useState<string>();
  /** Trace 数据状态 */
  const { sourceTextBlocks, polishedOutputContent, recallInfoText, loading, error } = useTraceContent(data);

  /** 检索结果组合结果（来自 searchDataList） */
  const sourceTextComposition = useMemo(
    () => composeSourceTextContent(sourceTextBlocks),
    [sourceTextBlocks]
  );
  const composedSourceText = sourceTextComposition.text; // 检索结果文本（来自 searchDataList）
  const sourceTextViewBlocks = sourceTextComposition.blocks;

  // 注意：rightViewMode 已移动到 useViewModeState hook 中管理
  const composedPolishedOutputText = useMemo(
    () => polishedOutputContent ?? '',
    [polishedOutputContent]
  );

  return {
    // 状态
    minLength,
    mode,
    freeMode,
    freeGroups,
    selectedMatchId,
    loading,
    error,
    composedSourceText,
    sourceTextViewBlocks,
    composedPolishedOutputText,
    recallInfoText,
    // 设置函数
    setMinLength,
    setMode,
    setFreeMode,
    setFreeGroups,
    setSelectedMatchId,
  };
};

