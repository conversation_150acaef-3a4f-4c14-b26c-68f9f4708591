import { useState, useRef } from 'react';
import type { ActiveOccurrence } from '../components/PlainTextViewer';

/**
 * 管理 active occurrence 状态和相关的 ref
 */
export const useActiveOccurrence = () => {
  const [sourceTextActiveOccurrence, setSourceTextActiveOccurrence] =
    useState<ActiveOccurrence>();
  const [polishedOutputActiveOccurrence, setPolishedOutputActiveOccurrence] =
    useState<ActiveOccurrence>();
  /** 自由模式新增高亮后，等待高亮数据同步完成再执行的自动点击任务。 */
  const pendingAutoSelectRef = useRef<ActiveOccurrence | null>(null);
  /** 存储右侧文本（检索结果或召回信息）高亮节点引用，支持滚动与聚焦。 */
  const sourceTextHighlightRefs = useRef<Record<string, HTMLElement[]>>({});
  /** 记录右侧文本侧循环索引，实现多 occurrence 轮询。 */
  const sourceTextCycleIndexRef = useRef<Record<string, number>>({});
  /** 记录润色出话侧循环索引，实现多 occurrence 轮询。 */
  const polishedOutputCycleIndexRef = useRef<Record<string, number>>({});
  /** 跟踪最近一次交互来源（右侧文本/左侧润色出话），辅助判断是否重置索引。 */
  const lastInteractionSourceRef = useRef<Record<string, 'sourceText' | 'polishedOutput'>>(
    {}
  );

  return {
    sourceTextActiveOccurrence,
    polishedOutputActiveOccurrence,
    pendingAutoSelectRef,
    sourceTextHighlightRefs,
    sourceTextCycleIndexRef,
    polishedOutputCycleIndexRef,
    lastInteractionSourceRef,
    setSourceTextActiveOccurrence,
    setPolishedOutputActiveOccurrence,
  };
};

