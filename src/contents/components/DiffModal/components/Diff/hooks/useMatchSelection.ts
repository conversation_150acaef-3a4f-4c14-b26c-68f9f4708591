import { useCallback, useEffect, useMemo, useRef } from 'react';
import type { HighlightRange } from '../types';
import type { ActiveOccurrence } from '../components/PlainTextViewer';
import type { PlainTextViewerHandle } from '../components/PlainTextViewer';
import type { SourceMode, MatchMode } from '../utils/scenario';
import { ScenarioOperator } from '../utils/scenarioHelpers';

interface UseMatchSelectionParams {
  selectedMatchId: string | undefined;
  setSelectedMatchId: (id: string | undefined) => void;
  sourceTextActiveOccurrence: ActiveOccurrence | undefined;
  polishedOutputActiveOccurrence: ActiveOccurrence | undefined;
  sourceTextHighlightRanges: HighlightRange[];
  polishedOutputHighlightRanges: HighlightRange[];
  sourceTextHighlightRefs: React.MutableRefObject<Record<string, HTMLElement[]>>;
  polishedOutputViewerRef: React.MutableRefObject<PlainTextViewerHandle | null>;
  recallInfoViewerRef: React.MutableRefObject<PlainTextViewerHandle | null>;
  sourceTextCycleIndexRef: React.MutableRefObject<Record<string, number>>;
  polishedOutputCycleIndexRef: React.MutableRefObject<Record<string, number>>;
  lastInteractionSourceRef: React.MutableRefObject<
    Record<string, 'sourceText' | 'polishedOutput'>
  >;
  setSourceTextActiveOccurrence: (occurrence: ActiveOccurrence | undefined) => void;
  setPolishedOutputActiveOccurrence: (occurrence: ActiveOccurrence | undefined) => void;
  rightViewMode: SourceMode;
  mode: MatchMode;
}

/**
 * 处理匹配选择的核心逻辑
 */
export const useMatchSelection = (params: UseMatchSelectionParams) => {
  const {
    selectedMatchId,
    setSelectedMatchId,
    sourceTextActiveOccurrence,
    polishedOutputActiveOccurrence,
    sourceTextHighlightRanges,
    polishedOutputHighlightRanges,
    sourceTextHighlightRefs,
    polishedOutputViewerRef,
    recallInfoViewerRef,
    sourceTextCycleIndexRef,
    polishedOutputCycleIndexRef,
    lastInteractionSourceRef,
    setSourceTextActiveOccurrence,
    setPolishedOutputActiveOccurrence,
    rightViewMode,
    mode,
  } = params;

  // 创建场景操作器
  const scenarioOperator = useMemo(
    () => new ScenarioOperator(mode, rightViewMode),
    [mode, rightViewMode]
  );

  /**
   * 聚焦右侧文本（检索结果或召回信息）中指定匹配的第 N 次出现，若当前节点已被移除则尝试回退到仍存在的节点。
   */
  const focusSourceTextMatch = useCallback(
    (matchId: string, occurrenceIndex = 0) => {
      const nodes = sourceTextHighlightRefs.current[matchId];
      if (!nodes || !nodes.length) {
        return;
      }
      const connected = nodes.filter((node) => node?.isConnected);
      if (!connected.length) {
        delete sourceTextHighlightRefs.current[matchId];
        return;
      }
      sourceTextHighlightRefs.current[matchId] = connected as HTMLElement[];
      const element = connected[occurrenceIndex] ?? connected[0];
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      element?.scrollIntoView({ behavior: 'instant' as any, block: 'center' });
      element?.focus?.({ preventScroll: true });
    },
    [sourceTextHighlightRefs]
  );

  /**
   * 处理右侧文本（检索结果或召回信息）或左侧润色出话的高亮点击/键盘选择：
   * - 记录最近互动来源，用于决定下一次循环的起点。
   * - 同步另一侧的高亮状态，实现左右联动。
   * - 支持循环遍历同一 matchId 下的多个 occurrence。
   */
  const handleMatchSelect = useCallback(
    (matchId: string, source: 'sourceText' | 'polishedOutput', occurrenceIndex = 0) => {
      console.log(matchId, source, occurrenceIndex);
      if (!matchId) {
        return;
      }
      // 标记当前 matchId 已被选中：
      // - `isSameMatch` 用于判断是否仍在操作同一组高亮，从而决定是否重置循环游标。
      // - `previousSource` 读取上一次触发该 matchId 的来源区域（右侧文本 / 左侧润色出话），
      //   便于后续判断是否需要重新从第 0 个 occurrence 开始遍历。
      const isSameMatch = selectedMatchId === matchId;
      const previousSource = lastInteractionSourceRef.current[matchId];
      setSelectedMatchId(matchId);
      if (!isSameMatch) {
        // 当切换到新的 matchId 时，清空两侧的循环游标，避免保留旧状态。
        sourceTextCycleIndexRef.current[matchId] = 0;
        polishedOutputCycleIndexRef.current[matchId] = 0;
      }
      if (source === 'sourceText') {
        /**
         * 处理右侧文本（检索结果或召回信息）区域的点击/键盘选择：
         * 1. 将 `sourceTextActiveOccurrence` 更新为当前 occurrence，控制右侧文本高亮的聚焦状态。
         * 2. 统计右侧文本中该 matchId 仍然挂载的节点数量，用于计算下一次循环时的 occurrence 索引。
         *    - 通过 `sourceTextCycleIndexRef` 储存下一次应聚焦的 occurrence，支持用户连续点击同一高亮，实现循环切换。
         * 3. 计算左侧润色出话区域的 occurrence 总数：
         *    - 若润色出话中没有对应高亮（通常意味着该匹配已被删除），则仅记录互动来源并清空润色出话聚焦。
         *    - 若存在匹配，则根据是否切换 matchId 或来源区，决定是否重置润色出话循环游标。
         * 4. 通过 `polishedOutputViewerRef` 主动滚动/聚焦左侧润色出话的指定 occurrence，实现左右联动。
         */
        setSourceTextActiveOccurrence({ matchId, occurrenceIndex });

        // 使用场景操作器获取右侧文本（检索结果或召回信息）的 occurrence 总数
        const sourceTextTotal = scenarioOperator.getSourceTextOccurrenceCount({
          matchId,
          recallInfoViewerRef,
          sourceTextHighlightRefs,
          sourceTextHighlightRanges,
        });

        sourceTextCycleIndexRef.current[matchId] = sourceTextTotal
          ? (occurrenceIndex + 1) % sourceTextTotal
          : 0;

        // 使用场景操作器获取左侧润色出话的 occurrence 总数
        const polishedOutputTotal = scenarioOperator.getPolishedOutputOccurrenceCount({
          matchId,
          polishedOutputViewerRef,
          polishedOutputHighlightRanges,
        });

        if (!polishedOutputTotal) {
          lastInteractionSourceRef.current[matchId] = source;
          setPolishedOutputActiveOccurrence(undefined);
          return;
        }
        const shouldResetPolishedOutputCycle =
          !isSameMatch || (previousSource !== 'sourceText');
        const nextIndex = shouldResetPolishedOutputCycle
          ? 0
          : polishedOutputCycleIndexRef.current[matchId] ?? 0;

        // 聚焦左侧润色出话的指定 occurrence
        polishedOutputViewerRef.current?.focusMatch(matchId, nextIndex);
        polishedOutputCycleIndexRef.current[matchId] = (nextIndex + 1) % polishedOutputTotal;
        setPolishedOutputActiveOccurrence({ matchId, occurrenceIndex: nextIndex });
      } else {
        /**
         * 处理左侧润色出话区域的点击/键盘选择，流程与右侧文本区域对称：
         * 1. 更新左侧润色出话的聚焦 occurrence，并维护 `polishedOutputCycleIndexRef`。
         * 2. 若右侧文本（检索结果或召回信息）中不存在对应节点，则只记录来源并清空右侧文本聚焦（可能是本地高亮消失的情况）。
         * 3. 当右侧文本存在匹配时，根据是否切换 matchId / 来源来决定是否从第 0 个 occurrence 重新开始循环。
         * 4. 调用场景操作器的 `focusSourceTextMatch` 将右侧文本滚动至对应 occurrence，保持左右视图同步。
         */
        setPolishedOutputActiveOccurrence({ matchId, occurrenceIndex });

        // 使用场景操作器获取右侧文本（检索结果或召回信息）的 occurrence 总数（基于 ranges）
        const sourceTextTotal = scenarioOperator.getSourceTextTotalFromRanges({
          matchId,
          recallInfoViewerRef,
          sourceTextHighlightRanges,
        });

        // 使用场景操作器获取连接的右侧文本（检索结果或召回信息）的 occurrence 数量
        const connectedSourceTextCount =
          scenarioOperator.getConnectedSourceTextOccurrenceCount({
            matchId,
            recallInfoViewerRef,
            sourceTextHighlightRefs,
            sourceTextTotal,
          });

        polishedOutputCycleIndexRef.current[matchId] = sourceTextTotal
          ? (occurrenceIndex + 1) % sourceTextTotal
          : 0;

        if (!sourceTextTotal || !connectedSourceTextCount) {
          lastInteractionSourceRef.current[matchId] = source;
          setSourceTextActiveOccurrence(undefined);
          return;
        }
        const shouldResetSourceTextCycle =
          !isSameMatch || (previousSource !== 'polishedOutput');
        const nextIndex = shouldResetSourceTextCycle
          ? 0
          : sourceTextCycleIndexRef.current[matchId] ?? 0;

        // 使用场景操作器聚焦右侧文本（检索结果或召回信息）
        scenarioOperator.focusSourceTextMatch({
          matchId,
          occurrenceIndex: nextIndex,
          recallInfoViewerRef,
          focusSourceTextMatchFn: focusSourceTextMatch,
        });

        sourceTextCycleIndexRef.current[matchId] = (nextIndex + 1) % sourceTextTotal;
        setSourceTextActiveOccurrence({ matchId, occurrenceIndex: nextIndex });
      }
      lastInteractionSourceRef.current[matchId] = source;
    },
    [
      focusSourceTextMatch,
      sourceTextHighlightRanges,
      scenarioOperator,
      selectedMatchId,
      polishedOutputHighlightRanges,
      setSourceTextActiveOccurrence,
      setPolishedOutputActiveOccurrence,
      sourceTextHighlightRefs,
      polishedOutputViewerRef,
      recallInfoViewerRef,
      sourceTextCycleIndexRef,
      polishedOutputCycleIndexRef,
      lastInteractionSourceRef,
      setSelectedMatchId,
    ]
  );

  /**
   * 监听高亮范围变化，清理已失效的循环索引，以及同步当前聚焦的匹配项。
   * 该副作用确保在切换模式或重新加载数据时，不会保留过期的引用。
   */
  useEffect(() => {
    const activeMatchIds = new Set<string>();
    sourceTextHighlightRanges.forEach((range) => activeMatchIds.add(range.matchId));
    polishedOutputHighlightRanges.forEach((range) => activeMatchIds.add(range.matchId));

    Object.keys(sourceTextCycleIndexRef.current).forEach((key) => {
      if (!activeMatchIds.has(key)) {
        delete sourceTextCycleIndexRef.current[key];
      }
    });
    Object.keys(polishedOutputCycleIndexRef.current).forEach((key) => {
      if (!activeMatchIds.has(key)) {
        delete polishedOutputCycleIndexRef.current[key];
      }
    });
    Object.keys(lastInteractionSourceRef.current).forEach((key) => {
      if (!activeMatchIds.has(key)) {
        delete lastInteractionSourceRef.current[key];
      }
    });
    if (
      sourceTextActiveOccurrence &&
      !activeMatchIds.has(sourceTextActiveOccurrence.matchId)
    ) {
      setSourceTextActiveOccurrence(undefined);
    }
    if (
      polishedOutputActiveOccurrence &&
      !activeMatchIds.has(polishedOutputActiveOccurrence.matchId)
    ) {
      setPolishedOutputActiveOccurrence(undefined);
    }
  }, [
    sourceTextActiveOccurrence,
    sourceTextHighlightRanges,
    polishedOutputActiveOccurrence,
    polishedOutputHighlightRanges,
    sourceTextCycleIndexRef,
    polishedOutputCycleIndexRef,
    lastInteractionSourceRef,
    setSourceTextActiveOccurrence,
    setPolishedOutputActiveOccurrence,
  ]);

  /**
   * 在自由选中模式下，当 sourceTextHighlightRanges 更新后，执行延迟的定位操作
   * 这确保在切换来源后，DOM 已经重新渲染并且 refs 已经更新
   */
  useEffect(() => {
    if (mode === 'free' && pendingFocusMatchIdRef.current) {
      const matchId = pendingFocusMatchIdRef.current;
      // 检查新的 sourceTextHighlightRanges 中是否包含该 matchId
      const hasMatch = sourceTextHighlightRanges.some(
        (range) => range.matchId === matchId
      );
      if (hasMatch) {
        // 使用双重 requestAnimationFrame 确保 DOM 已经渲染完成
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            scenarioOperator.focusSourceTextMatch({
              matchId,
              occurrenceIndex: 0,
              recallInfoViewerRef,
              focusSourceTextMatchFn: focusSourceTextMatch,
            });
            // 清除待处理的定位标记
            pendingFocusMatchIdRef.current = undefined;
          });
        });
      } else {
        // 如果没有匹配，清除待处理的定位标记
        pendingFocusMatchIdRef.current = undefined;
      }
    }
  }, [
    mode,
    sourceTextHighlightRanges,
    scenarioOperator,
    recallInfoViewerRef,
    focusSourceTextMatch,
  ]);

  /**
   * 跟踪上一次的 rightViewMode，用于检测来源切换
   */
  const prevRightViewModeRef = useRef(rightViewMode);
  /**
   * 跟踪上一次切换来源前选中的文本位置，用于在切换来源后保持左侧选中状态
   */
  const prevPolishedOutputRangeRef = useRef<{ start: number; end: number } | undefined>(
    undefined
  );
  /**
   * 在自由选中模式下，跟踪切换来源后需要延迟定位的 matchId
   */
  const pendingFocusMatchIdRef = useRef<string | undefined>(undefined);

  /**
   * 当 polishedOutputActiveOccurrence 变化时（非切换来源导致），记录选中的文本位置
   */
  useEffect(() => {
    if (
      prevRightViewModeRef.current === rightViewMode &&
      polishedOutputActiveOccurrence
    ) {
      const range = polishedOutputHighlightRanges.find(
        (r) =>
          r.matchId === polishedOutputActiveOccurrence.matchId &&
          // 找到对应 occurrenceIndex 的 range（需要计算这是第几个相同 matchId 的 range）
          (() => {
            let count = 0;
            for (const r2 of polishedOutputHighlightRanges) {
              if (r2.matchId === polishedOutputActiveOccurrence.matchId) {
                if (r2 === r) {
                  return count === polishedOutputActiveOccurrence.occurrenceIndex;
                }
                count++;
              }
            }
            return false;
          })()
      );
      if (range) {
        prevPolishedOutputRangeRef.current = { start: range.start, end: range.end };
      }
    }
  }, [polishedOutputActiveOccurrence, polishedOutputHighlightRanges, rightViewMode]);

  /**
   * 当切换来源时，处理选中状态的更新：
   * 1. 如果原先点击的色块仍命中，润色出话中原先选中的色块不变，来源处定位为新来源中第一个匹配项
   * 2. 如果未命中，视为未点击，清除选中状态
   */
  useEffect(() => {
    const hasSourceChanged = prevRightViewModeRef.current !== rightViewMode;
    if (hasSourceChanged) {
      prevRightViewModeRef.current = rightViewMode;

      if (!selectedMatchId || !prevPolishedOutputRangeRef.current) {
        return;
      }

      // 在新的 polishedOutputHighlightRanges 中查找相同位置的 range
      const { start, end } = prevPolishedOutputRangeRef.current;
      const matchingRange = polishedOutputHighlightRanges.find(
        (range) => range.start === start && range.end === end
      );

      if (matchingRange) {
        // 找到了相同位置的 range，使用新的 matchId
        const newMatchId = matchingRange.matchId;

        // 计算新的 occurrenceIndex
        let newOccurrenceIndex = 0;
        for (const range of polishedOutputHighlightRanges) {
          if (range.matchId === newMatchId) {
            if (range.start === start && range.end === end) {
              break;
            }
            newOccurrenceIndex++;
          }
        }

        // 检查新来源中是否有匹配
        const hasSourceTextMatch = sourceTextHighlightRanges.some(
          (range) => range.matchId === newMatchId
        );

        if (hasSourceTextMatch) {
          // 更新 selectedMatchId 和 polishedOutputActiveOccurrence，保持左侧选中状态不变
          setSelectedMatchId(newMatchId);
          setPolishedOutputActiveOccurrence({
            matchId: newMatchId,
            occurrenceIndex: newOccurrenceIndex,
          });

          // 来源处定位为新来源中的第一个匹配项（occurrenceIndex = 0）
          setSourceTextActiveOccurrence({
            matchId: newMatchId,
            occurrenceIndex: 0,
          });
          console.log(11, newMatchId);
          // 定位到新来源的第一个匹配项
          // 在自由选中模式下，需要等待 sourceTextHighlightRanges 更新和 DOM 渲染完成后再定位
          // 因为自由选中模式下，切换来源会触发 sourceTextHighlightRanges 的重新计算
          if (mode === 'free') {
            // 标记需要延迟定位，等待 sourceTextHighlightRanges 更新后再执行
            pendingFocusMatchIdRef.current = newMatchId;
          } else {
            // 其他模式直接定位
            scenarioOperator.focusSourceTextMatch({
              matchId: newMatchId,
              occurrenceIndex: 0,
              recallInfoViewerRef,
              focusSourceTextMatchFn: focusSourceTextMatch,
            });
          }

          // 将切换来源后定位到第一个视为已点击左侧一次
          // 这样下次点击左侧时会直接定位到第二个
          sourceTextCycleIndexRef.current[newMatchId] = 1;
          lastInteractionSourceRef.current[newMatchId] = 'polishedOutput';
        } else {
          // 新来源中没有匹配，清除选中状态
          setSelectedMatchId(undefined);
          setSourceTextActiveOccurrence(undefined);
          setPolishedOutputActiveOccurrence(undefined);
          prevPolishedOutputRangeRef.current = undefined;
        }
      } else {
        // 找不到相同位置的 range，说明该文本已被移除，清除选中状态
        setSelectedMatchId(undefined);
        setSourceTextActiveOccurrence(undefined);
        setPolishedOutputActiveOccurrence(undefined);
        prevPolishedOutputRangeRef.current = undefined;
      }
      return;
    }

    // 非切换来源的情况，正常处理
    prevRightViewModeRef.current = rightViewMode;

    // 如果 selectedMatchId 不存在了，清除选中状态
    if (selectedMatchId) {
      const hasSourceTextMatch = sourceTextHighlightRanges.some(
        (range) => range.matchId === selectedMatchId
      );
      const hasPolishedOutputMatch = polishedOutputHighlightRanges.some(
        (range) => range.matchId === selectedMatchId
      );

      if (!hasSourceTextMatch && !hasPolishedOutputMatch) {
        setSelectedMatchId(undefined);
        setSourceTextActiveOccurrence(undefined);
        setPolishedOutputActiveOccurrence(undefined);
        prevPolishedOutputRangeRef.current = undefined;
      }
    }
  }, [
    mode,
    sourceTextHighlightRanges,
    selectedMatchId,
    polishedOutputHighlightRanges,
    rightViewMode,
    sourceTextCycleIndexRef,
    lastInteractionSourceRef,
    scenarioOperator,
    recallInfoViewerRef,
    focusSourceTextMatch,
    setSelectedMatchId,
    setSourceTextActiveOccurrence,
    setPolishedOutputActiveOccurrence,
  ]);

  return {
    handleMatchSelect,
    focusSourceTextMatch,
  };
};
