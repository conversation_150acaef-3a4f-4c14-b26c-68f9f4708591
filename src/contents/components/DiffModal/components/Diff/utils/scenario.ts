import type { HighlightRange } from '../types';
import { getNumberHighlightRanges, getTextHighlightRanges, filterHighlightOverlaps } from './';
import { collectSelectionRanges } from './free';

/** 来源选择类型 */
export type SourceMode = 'search-result' | 'recall-info';

/** 匹配逻辑类型 */
export type MatchMode = 'numbers' | 'text' | 'free';

/** 场景配置：6种组合场景 (MatchMode × SourceMode) */
export type ScenarioKey = `${MatchMode}-${SourceMode}`;

/** 场景配置接口 */
export interface ScenarioConfig {
  /** 匹配逻辑 */
  matchMode: MatchMode;
  /** 来源选择 */
  sourceMode: SourceMode;
  /** 计算高亮范围的函数 */
  calculateHighlightRanges: (params: {
    sourceText: string;
    polishedOutputText: string;
    minLength: number;
    freeGroups: import('../types').SelectionGroup[];
  }) => { sourceTextRanges: HighlightRange[]; polishedOutputRanges: HighlightRange[] };
  /** 获取右侧文本的函数（检索结果或召回信息） */
  getSourceText: (params: {
    composedSourceText: string; // 检索结果文本（来自 searchDataList）
    recallInfoText: string; // 召回信息文本（来自 currentQuery）
  }) => string;
  /** 获取左侧润色出话的函数（来自 output） */
  getPolishedOutputText: (params: {
    composedPolishedOutputText: string; // 润色出话文本（来自 output）
    recallInfoText: string;
  }) => string;
}

/**
 * 场景策略映射表：定义6种场景的配置
 */
const SCENARIO_STRATEGIES: Record<ScenarioKey, Omit<ScenarioConfig, 'matchMode' | 'sourceMode'>> = {
  // 数字匹配 + 检索结果：检索结果（searchDataList）与润色出话（output）的匹配
  'numbers-search-result': {
    calculateHighlightRanges: ({ sourceText, polishedOutputText }) =>
      getNumberHighlightRanges(sourceText, polishedOutputText),
    getSourceText: ({ composedSourceText }) => composedSourceText, // 检索结果文本
    getPolishedOutputText: ({ composedPolishedOutputText }) => composedPolishedOutputText, // 润色出话文本
  },

  // 数字匹配 + 召回信息：召回信息（currentQuery）与润色出话（output）的匹配
  'numbers-recall-info': {
    calculateHighlightRanges: ({ sourceText, polishedOutputText }) =>
      getNumberHighlightRanges(sourceText, polishedOutputText),
    getSourceText: ({ recallInfoText }) => recallInfoText, // 召回信息文本
    getPolishedOutputText: ({ composedPolishedOutputText }) => composedPolishedOutputText, // 润色出话文本
  },

  // 长段文本匹配 + 检索结果：检索结果（searchDataList）与润色出话（output）的匹配
  'text-search-result': {
    calculateHighlightRanges: ({ sourceText, polishedOutputText, minLength }) =>
      getTextHighlightRanges(sourceText, polishedOutputText, minLength),
    getSourceText: ({ composedSourceText }) => composedSourceText, // 检索结果文本
    getPolishedOutputText: ({ composedPolishedOutputText }) => composedPolishedOutputText, // 润色出话文本
  },

  // 长段文本匹配 + 召回信息：召回信息（currentQuery）与润色出话（output）的匹配
  'text-recall-info': {
    calculateHighlightRanges: ({ sourceText, polishedOutputText, minLength }) =>
      getTextHighlightRanges(sourceText, polishedOutputText, minLength),
    getSourceText: ({ recallInfoText }) => recallInfoText, // 召回信息文本
    getPolishedOutputText: ({ composedPolishedOutputText }) => composedPolishedOutputText, // 润色出话文本
  },

  // 自由选择 + 检索结果：检索结果（searchDataList）与润色出话（output）的匹配
  'free-search-result': {
    calculateHighlightRanges: ({ freeGroups }) =>
      collectSelectionRanges(freeGroups),
    getSourceText: ({ composedSourceText }) => composedSourceText, // 检索结果文本
    getPolishedOutputText: ({ composedPolishedOutputText }) => composedPolishedOutputText, // 润色出话文本
  },

  // 自由选择 + 召回信息：召回信息（currentQuery）与润色出话（output）的匹配
  'free-recall-info': {
    calculateHighlightRanges: ({ freeGroups }) =>
      collectSelectionRanges(freeGroups),
    getSourceText: ({ recallInfoText }) => recallInfoText, // 召回信息文本
    getPolishedOutputText: ({ composedPolishedOutputText }) => composedPolishedOutputText, // 润色出话文本
  },
};

/**
 * 生成场景key
 */
export const getScenarioKey = (
  matchMode: MatchMode,
  sourceMode: SourceMode
): ScenarioKey => `${matchMode}-${sourceMode}`;

/**
 * 获取场景配置
 */
export const getScenarioConfig = (
  matchMode: MatchMode,
  sourceMode: SourceMode
): ScenarioConfig => {
  const key = getScenarioKey(matchMode, sourceMode);
  const strategy = SCENARIO_STRATEGIES[key];
  return {
    matchMode,
    sourceMode,
    ...strategy,
  };
};

/**
 * 计算当前场景的高亮范围
 * 所有模式的匹配完成后，统一进行重叠检查，保留最长的重叠匹配。
 */
export const calculateScenarioHighlightRanges = (
  matchMode: MatchMode,
  sourceMode: SourceMode,
  params: {
    composedSourceText: string;
    composedPolishedOutputText: string;
    recallInfoText: string;
    minLength: number;
    freeGroups: import('../types').SelectionGroup[];
  }
): { sourceTextRanges: HighlightRange[]; polishedOutputRanges: HighlightRange[] } => {
  const config = getScenarioConfig(matchMode, sourceMode);

  const sourceText = config.getSourceText({
    composedSourceText: params.composedSourceText,
    recallInfoText: params.recallInfoText,
  });

  const polishedOutputText = config.getPolishedOutputText({
    composedPolishedOutputText: params.composedPolishedOutputText,
    recallInfoText: params.recallInfoText,
  });

  // 先按各模式的规则进行匹配
  const rawRanges = config.calculateHighlightRanges({
    sourceText,
    polishedOutputText,
    minLength: params.minLength,
    freeGroups: params.freeGroups,
  });

  // 先分别对左右两侧进行重叠检查，保留最长的重叠匹配
  const filteredSourceTextRanges = filterHighlightOverlaps(rawRanges.sourceTextRanges);
  const filteredPolishedOutputRanges = filterHighlightOverlaps(rawRanges.polishedOutputRanges);

  // 收集两侧都存在的 matchId，确保左右两侧的 ranges 保持关联
  const sourceTextMatchIds = new Set(filteredSourceTextRanges.map((r) => r.matchId));
  const polishedOutputMatchIds = new Set(filteredPolishedOutputRanges.map((r) => r.matchId));
  const validMatchIds = new Set<string>();

  // 找出两侧都存在的 matchId
  sourceTextMatchIds.forEach((matchId) => {
    if (polishedOutputMatchIds.has(matchId)) {
      validMatchIds.add(matchId);
    }
  });

  // 只保留两侧都存在的 matchId 对应的 ranges
  return {
    sourceTextRanges: filteredSourceTextRanges.filter((r) => validMatchIds.has(r.matchId)),
    polishedOutputRanges: filteredPolishedOutputRanges.filter((r) => validMatchIds.has(r.matchId)),
  };
};

