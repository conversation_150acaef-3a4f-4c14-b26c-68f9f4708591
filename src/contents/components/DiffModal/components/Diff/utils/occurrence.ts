import type { HighlightRange } from '../types';

/** 为 matchId + 范围组合生成唯一 key，用于 occurrence 映射。 */
export const getOccurrenceKey = (matchId: string, start: number, end: number) =>
  `${matchId}-${start}-${end}`;

/**
 * 计算右侧文本（检索结果或召回信息）侧 occurrence 索引映射
 */
export const buildSourceTextOccurrenceIndexMap = (
  sourceTextHighlightRanges: HighlightRange[]
): Map<string, number> => {
  const indexMap = new Map<string, number>();
  const counters = new Map<string, number>();
  const sortedRanges = [...sourceTextHighlightRanges].sort(
    (a, b) => a.start - b.start || a.end - b.end
  );
  sortedRanges.forEach((range) => {
    const nextIndex = counters.get(range.matchId) ?? 0;
    counters.set(range.matchId, nextIndex + 1);
    indexMap.set(
      getOccurrenceKey(range.matchId, range.start, range.end),
      nextIndex
    );
  });
  return indexMap;
};

/**
 * 计算 occurrence 摘要信息（当前序号、总数、匹配文本）
 */
export const buildOccurrenceSummary = (
  activeOccurrence: { matchId: string; occurrenceIndex: number } | undefined,
  highlightRanges: HighlightRange[],
  composedText: string
): { current: number; total: number; text: string } | undefined => {
  if (!activeOccurrence) {
    return undefined;
  }
  const { matchId, occurrenceIndex } = activeOccurrence;
  if (!matchId) {
    return undefined;
  }
  const ranges = highlightRanges
    .filter((range) => range.matchId === matchId)
    .sort((a, b) => a.start - b.start || a.end - b.end);
  const total = ranges.length;
  if (!total) {
    return undefined;
  }
  const safeIndex = Math.min(Math.max(occurrenceIndex, 0), total - 1);
  const currentRange = ranges[safeIndex];
  const matchedText = currentRange
    ? composedText.slice(currentRange.start, currentRange.end)
    : '';
  return {
    current: safeIndex + 1,
    total,
    text: matchedText,
  };
};

