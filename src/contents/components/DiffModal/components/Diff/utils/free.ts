import type { FreeSelectionPayload, HighlightRange, SelectionGroup } from '../types';
import { generateSoftColor } from './color';

/** 自由选择模式下的配色方案，和文本模式保持一致的柔和风格。 */
const buildColor = (index: number) => generateSoftColor(index);

/** 软封装一次文本查找，避免重复实现。 */
const findAllOccurrences = (haystack: string, needle: string) => {
  if (!needle.length) {
    return [];
  }
  const positions: number[] = [];
  let index = 0;
  const step = Math.max(needle.length, 1);
  while (index <= haystack.length - needle.length) {
    const found = haystack.indexOf(needle, index);
    if (found === -1) {
      break;
    }
    positions.push(found);
    index = found + step;
  }
  return positions;
};

type TryAddGroupParams = {
  selection?: FreeSelectionPayload;
  groups: SelectionGroup[];
  sourceText: string;
  polishedOutputText: string;
};

type TryAddGroupResult =
  | { type: 'noop' }
  | { type: 'warning'; message: string }
  | { type: 'success'; groups: SelectionGroup[] };

/**
 * 尝试将一次用户选区加入自由高亮分组：
 * 1. 校验选区与历史结果是否重复或完全覆盖。
 * 2. 确认右侧文本（检索结果或召回信息）中存在对应片段，否则给出提示。
 * 3. 生成新的 matchId、颜色，并同步 sourceText/polishedOutput 的高亮区间。
 */
export const tryAddSelectionGroup = ({
  selection,
  groups,
  sourceText,
  polishedOutputText,
}: TryAddGroupParams): TryAddGroupResult => {
  if (!selection) {
    return { type: 'noop' };
  }

  const { text, start, end } = selection;
  if (!text.trim()) {
    return { type: 'noop' };
  }

  const overlapsExisting = groups.some((group) =>
    group.polishedOutputRanges.some((range) => start < range.end && end > range.start),
  );
  if (overlapsExisting) {
    return { type: 'warning', message: '该区域已被高亮，请选择其他文本' };
  }

  const sourceTextPositions = findAllOccurrences(sourceText, text);
  if (!sourceTextPositions.length) {
    return { type: 'warning', message: '原始文本中没有找到匹配内容' };
  }

  const polishedOutputPositions = findAllOccurrences(polishedOutputText, text);

  const isDuplicate = groups.some(
    (group) => group.text === text && group.anchor.start === start && group.anchor.end === end,
  );
  if (isDuplicate) {
    return { type: 'noop' };
  }

  const color = buildColor(groups.length);
  const matchId = `free-${Date.now()}-${groups.length}`;

  const sourceTextRanges: HighlightRange[] = sourceTextPositions.map((position) => ({
    start: position,
    end: position + text.length,
    color,
    matchId,
  }));

  const polishedOutputRanges: HighlightRange[] =
    polishedOutputPositions.length > 0
      ? polishedOutputPositions.map((position) => ({
          start: position,
          end: position + text.length,
          color,
          matchId,
        }))
      : [
          {
            start,
            end,
            color,
            matchId,
          },
        ];

  return {
    type: 'success',
    groups: [
      ...groups,
      {
        matchId,
        color,
        text,
        anchor: { start, end },
        sourceTextRanges,
        polishedOutputRanges,
      },
    ],
  };
};

/**
 * 收集所有自由选择分组里的高亮范围，供渲染层直接使用。
 * 注意：这里不进行重叠过滤，重叠过滤应该在统一的地方进行。
 */
export const collectSelectionRanges = (groups: SelectionGroup[]) => ({
  sourceTextRanges: groups.flatMap((group) => group.sourceTextRanges),
  polishedOutputRanges: groups.flatMap((group) => group.polishedOutputRanges),
});
