import type { HighlightRange } from '../types';

/**
 * 数字（或日期时间）在文本中的一次出现，用于做多对多高亮。
 */
type NumberOccurrence = {
  /** 用于匹配的文本值 */
  searchValue: string;
  /** 在文本中的起始下标（包含） */
  start: number;
  /** 在文本中的结束下标（不包含） */
  end: number;
};

/**
 * 统一匹配数字、日期、时间、百分比等结构的正则表达式。
 */
const numberPattern =
  /\d{4}[年/-]\d{1,2}[月/-]\d{1,2}[日号]?(?:[T\s]\d{1,2}[:：]\d{2}(?::\d{2})?)?|\d{1,2}[月/-]\d{1,2}[日号]?|\d{1,2}[:：]\d{2}(?::\d{2})?|[+-]?\d+(?:[.,]\d+)*(?:%|％|‰|‱)?/g;

/**
 * 将全角符号转换为半角
 */
const toHalfWidth = (value: string) =>
  value.replace(/[！-～]/g, (char) => String.fromCharCode(char.charCodeAt(0) - 0xfee0));

/**
 * 提取数字值
 */
const extractNumberValue = (match: RegExpExecArray): NumberOccurrence => {
  const rawValue = match[0];
  const start = match.index!;
  const end = match.index! + rawValue.length;

  // 转换为半角
  let searchValue = toHalfWidth(rawValue);

  // 统一全角符号为半角
  searchValue = searchValue
    .replace(/[﹣－‐‑‒–—−]/g, '-')
    .replace(/[﹢＋]/g, '+')
    .replace(/[％]/g, '%')
    .replace(/[，]/g, ',')
    .replace(/[：]/g, ':');

  // 统一全角数字为半角
  searchValue = searchValue.replace(/[０-９]/g, (char) =>
    String.fromCharCode(char.charCodeAt(0) - 0xff10 + 48),
  );

  // 移除多余空格
  searchValue = searchValue.replace(/\s+/g, '');

  return {
    searchValue,
    start,
    end,
  };
};

/**
 * 扫描文本中的所有数字/日期时间
 */
const extractNumberOccurrences = (text: string): NumberOccurrence[] => {
  const results: NumberOccurrence[] = [];
  numberPattern.lastIndex = 0;
  let match: RegExpExecArray | null = null;
  while ((match = numberPattern.exec(text)) !== null) {
    const occurrence = extractNumberValue(match);
    results.push(occurrence);
  }
  return results;
};

/**
 * 检查两个范围是否重叠
 */
const isOverlapping = (range1: { start: number; end: number }, range2: { start: number; end: number }): boolean => {
  return range1.start < range2.end && range2.start < range1.end;
};

/**
 * 在文本中查找指定数字值的所有出现位置，排除已占用的位置
 * 使用 indexOf 直接查找，和文本匹配逻辑一致
 */
const findAllOccurrences = (haystack: string, needle: string): number[] => {
  if (!needle.length) {
    return [];
  }
  const positions: number[] = [];
  let index = 0;
  // 使用步长为1，确保找到所有匹配（包括重叠的）
  while (index <= haystack.length - needle.length) {
    const found = haystack.indexOf(needle, index);
    if (found === -1) {
      break;
    }
    positions.push(found);
    // 步长为1，确保不遗漏任何匹配
    index = found + 1;
  }
  return positions;
};

/**
 * 在文本中查找与指定数字匹配的所有出现位置，排除已占用的位置
 */
const findMatchingOccurrences = (
  searchText: string,
  searchValue: string,
  excludedRanges: Array<{ start: number; end: number }> = [],
): NumberOccurrence[] => {
  const positions = findAllOccurrences(searchText, searchValue);
  const matches: NumberOccurrence[] = [];

  for (const start of positions) {
    const end = start + searchValue.length;
    
    // 检查是否与已占用的位置重叠
    const isExcluded = excludedRanges.some((excluded) =>
      isOverlapping(excluded, { start, end }),
    );
    if (isExcluded) {
      continue;
    }

    matches.push({
      searchValue,
      start,
      end,
    });
  }

  return matches;
};

/**
 * 生成数字模式下的高亮范围：
 * 1. 先从润色出话（polishedOutput）中提取所有数字（包含格式化后的日期和数字，处理小数、负数和百分比）
 * 2. 按长度排序，优先处理更长的数字（避免重叠时短数字覆盖长数字）
 * 3. 然后在来源文本（sourceText）中查找是否有对应值（使用 indexOf 直接查找，和文本匹配逻辑一致）
 * 4. 如果找到匹配，则高亮显示来源文本和润色出话中所有相同的数字
 */
export const getNumberHighlightRanges = (
  sourceText: string,
  polishedOutput: string,
): { sourceTextRanges: HighlightRange[]; polishedOutputRanges: HighlightRange[] } => {
  // 步骤1：从润色出话中提取所有数字
  let polishedOutputNumbers = extractNumberOccurrences(polishedOutput);

  // 过滤掉长度小于2的数字
  polishedOutputNumbers = polishedOutputNumbers.filter(
    (occurrence) => occurrence.searchValue.length >= 2,
  );

  // 步骤2：按长度排序（从长到短），优先处理更长的数字
  polishedOutputNumbers = polishedOutputNumbers.sort((a, b) => {
    const lengthA = a.end - a.start;
    const lengthB = b.end - b.start;
    return lengthB - lengthA; // 降序排列
  });

  const sourceTextRanges: HighlightRange[] = [];
  const polishedOutputRanges: HighlightRange[] = [];
  
  // 跟踪已占用的位置，避免重叠
  const usedSourceTextRanges: Array<{ start: number; end: number }> = [];
  const usedPolishedOutputRanges: Array<{ start: number; end: number }> = [];

  // 步骤3：对每个润色出话中的数字，在来源文本中查找匹配
  polishedOutputNumbers.forEach((polishedOutputOccurrence, index) => {
    // 检查润色出话中的位置是否已被占用
    const isPolishedOutputUsed = usedPolishedOutputRanges.some((used) =>
      isOverlapping(used, { start: polishedOutputOccurrence.start, end: polishedOutputOccurrence.end }),
    );
    if (isPolishedOutputUsed) {
      return; // 跳过已被更长的数字占用的位置
    }

    // 在来源文本中查找匹配，排除已占用的位置
    const sourceTextMatches = findMatchingOccurrences(
      sourceText,
      polishedOutputOccurrence.searchValue,
      usedSourceTextRanges,
    );

    // 如果找到匹配，则生成高亮范围
    if (sourceTextMatches.length > 0) {
      const matchId = `number-${index}`;
      const color = 'rgb(223, 222, 247)';

      // 高亮来源文本中所有匹配的位置
      sourceTextMatches.forEach((match) => {
        sourceTextRanges.push({
          start: match.start,
          end: match.end,
          matchId,
          color,
        });
        // 记录已占用的位置
        usedSourceTextRanges.push({ start: match.start, end: match.end });
      });

      // 在润色出话中查找所有相同的数字（包括当前这个），排除已占用的位置
      const polishedOutputMatches = findMatchingOccurrences(
        polishedOutput,
        polishedOutputOccurrence.searchValue,
        usedPolishedOutputRanges,
      );
      
      // 高亮润色出话中所有匹配的位置
      polishedOutputMatches.forEach((match) => {
        polishedOutputRanges.push({
          start: match.start,
          end: match.end,
          matchId,
          color,
        });
        // 记录已占用的位置
        usedPolishedOutputRanges.push({ start: match.start, end: match.end });
      });
    }
  });

  // 注意：重叠过滤在 calculateScenarioHighlightRanges 中统一进行
  return {
    sourceTextRanges,
    polishedOutputRanges,
  };
};

