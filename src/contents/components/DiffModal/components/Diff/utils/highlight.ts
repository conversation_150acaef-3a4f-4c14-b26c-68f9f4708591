import type { HighlightRange, Segment } from '../types';
import { generateSoftColor } from './color';
import { findAllOccurrences, findTextMatches } from './text';

/**
 * 判断两个范围是否重叠
 */
const isOverlappingRange = (a: HighlightRange, b: HighlightRange): boolean => {
  return a.start < b.end && b.start < a.end;
};

/**
 * 获取范围的长度
 */
const getRangeLength = (range: HighlightRange): number => {
  return range.end - range.start;
};

/**
 * 对同一文本的高亮区间做去重：当重叠时保留较长的区间。
 * 按长度降序排序，优先处理更长的区间，确保重叠时保留最长的。
 */
export const filterHighlightOverlaps = (ranges: HighlightRange[]): HighlightRange[] => {
  if (!ranges.length) {
    return [];
  }

  // 按长度降序排序，优先处理更长的区间
  const sorted = [...ranges].sort((a, b) => {
    const lenA = getRangeLength(a);
    const lenB = getRangeLength(b);
    if (lenA !== lenB) {
      return lenB - lenA; // 长度降序
    }
    // 长度相同，按位置排序
    if (a.start !== b.start) {
      return a.start - b.start;
    }
    return a.end - b.end;
  });

  const result: HighlightRange[] = [];

  for (const range of sorted) {
    // 检查是否与已保留的范围重叠
    const overlaps = result.some((existing) => isOverlappingRange(range, existing));

    // 如果没有重叠，保留它
    if (!overlaps) {
      result.push(range);
    }
  }

  // 最后按位置排序返回
  return result.sort((a, b) => {
    if (a.start !== b.start) {
      return a.start - b.start;
    }
    return a.end - b.end;
  });
};

/**
 * 将高亮范围转换为便于渲染的片段列表，保证不同区间交替输出。
 */
type RangeWithOccurrence = HighlightRange & { occurrenceIndex?: number };

export const buildSegments = (
  text: string,
  ranges: RangeWithOccurrence[],
  keyPrefix: string,
): Segment[] => {
  const sortedRanges = [...ranges].sort((a, b) => a.start - b.start);
  const segments: Segment[] = [];
  let cursor = 0;

  sortedRanges.forEach((range, index) => {
    if (cursor < range.start) {
      segments.push({
        id: `${keyPrefix}-plain-${index}-${cursor}`,
        text: text.slice(cursor, range.start),
        highlight: false,
      });
    }

    segments.push({
      id: `${keyPrefix}-highlight-${index}-${range.start}`,
      text: text.slice(range.start, range.end),
      highlight: true,
      color: range.color,
      matchId: range.matchId,
      occurrenceIndex: range.occurrenceIndex,
    });

    cursor = range.end;
  });

  if (cursor < text.length) {
    segments.push({
      id: `${keyPrefix}-plain-final-${cursor}`,
      text: text.slice(cursor),
      highlight: false,
    });
  }

  return segments;
};

/**
 * 生成长文本模式下的高亮范围：
 * - 先根据阈值筛选匹配
 * - 为每个匹配生成独立颜色/分组
 * - polishedOutputRanges 的 matchId 和 color 基于 polishedOutput 本身生成，保持稳定，不随 sourceText 变化
 */
export const getTextHighlightRanges = (
  sourceText: string,
  polishedOutput: string,
  minLength: number,
): { sourceTextRanges: HighlightRange[]; polishedOutputRanges: HighlightRange[] } => {
  const matches = findTextMatches(sourceText, polishedOutput, minLength);

  const sourceTextRanges: HighlightRange[] = [];
  const polishedOutputRanges: HighlightRange[] = [];
  
  // 为 polishedOutput 中的每个唯一文本片段生成稳定的 matchId 和 color
  // 使用文本内容在 polishedOutput 中首次出现的位置作为索引，确保稳定性
  const polishedOutputTextGroups = new Map<string, { matchId: string; color: string; firstIndex: number }>();
  
  // 先收集所有匹配的文本内容，并按在 polishedOutput 中首次出现的位置排序
  const uniqueTexts = new Set<string>();
  matches.forEach((match) => {
    const textContent = match.text;
    if (textContent.trim()) {
      uniqueTexts.add(textContent);
    }
  });
  
  // 为每个文本内容找到在 polishedOutput 中的首次出现位置，并分配稳定的 matchId 和 color
  const sortedTexts = Array.from(uniqueTexts).sort((a, b) => {
    const indexA = polishedOutput.indexOf(a);
    const indexB = polishedOutput.indexOf(b);
    return indexA - indexB;
  });
  
  sortedTexts.forEach((textContent, index) => {
    const firstIndex = polishedOutput.indexOf(textContent);
    polishedOutputTextGroups.set(textContent, {
      matchId: `text-${index}`,
      color: generateSoftColor(index),
      firstIndex,
    });
  });

  // 为每个文本内容生成高亮范围
  polishedOutputTextGroups.forEach(({ matchId, color }, textContent) => {
    // 在 sourceText 中查找匹配
    const sourceTextOccurrences = findAllOccurrences(sourceText, textContent);
    sourceTextOccurrences.forEach((start) => {
      sourceTextRanges.push({
        start,
        end: start + textContent.length,
        matchId,
        color,
      });
    });

    // 在 polishedOutput 中查找所有出现位置
    const polishedOutputOccurrences = findAllOccurrences(polishedOutput, textContent);
    polishedOutputOccurrences.forEach((start) => {
      polishedOutputRanges.push({
        start,
        end: start + textContent.length,
        matchId,
        color,
      });
    });
  });

  // 注意：重叠过滤在 calculateScenarioHighlightRanges 中统一进行
  return {
    sourceTextRanges,
    polishedOutputRanges,
  };
};

