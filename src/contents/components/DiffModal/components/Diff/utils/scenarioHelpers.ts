import type { SourceMode, MatchMode } from './scenario';
import { ScenarioContext as ScenarioContextClass } from './scenarioContext';

/**
 * 创建场景上下文实例
 */
export const createScenarioContext = (
  matchMode: MatchMode,
  sourceMode: SourceMode
): ScenarioContextClass => {
  return new ScenarioContextClass(matchMode, sourceMode);
};

/**
 * 场景操作器：提供场景相关的操作方法
 */
export class ScenarioOperator {
  private context: ScenarioContextClass;

  constructor(matchMode: MatchMode, sourceMode: SourceMode) {
    this.context = createScenarioContext(matchMode, sourceMode);
  }

  /**
   * 获取右侧文本（检索结果或召回信息）的 occurrence 总数
   */
  getSourceTextOccurrenceCount(params: {
    matchId: string;
    recallInfoViewerRef: React.MutableRefObject<import('../components/PlainTextViewer').PlainTextViewerHandle | null>;
    sourceTextHighlightRefs: React.MutableRefObject<Record<string, HTMLElement[]>>;
    sourceTextHighlightRanges: import('../types').HighlightRange[];
  }): number {
    if (this.context.isRecallInfoMode()) {
      // 召回信息模式：使用 recallInfoViewerRef 获取 occurrence 数量
      return (
        params.recallInfoViewerRef.current?.getOccurrenceCount(params.matchId) ??
        0
      );
    }
    // 检索结果模式：使用 sourceTextHighlightRefs
    return params.sourceTextHighlightRefs.current[params.matchId]
      ? params.sourceTextHighlightRefs.current[params.matchId].filter(
          (node) => node?.isConnected
        ).length
      : 0;
  }

  /**
   * 获取连接的右侧文本（检索结果或召回信息）的 occurrence 数量（已挂载到 DOM 的节点）
   */
  getConnectedSourceTextOccurrenceCount(params: {
    matchId: string;
    recallInfoViewerRef: React.MutableRefObject<import('../components/PlainTextViewer').PlainTextViewerHandle | null>;
    sourceTextHighlightRefs: React.MutableRefObject<Record<string, HTMLElement[]>>;
    sourceTextTotal: number;
  }): number {
    if (this.context.isRecallInfoMode()) {
      // 召回信息模式：connectedSourceTextCount 等于 sourceTextTotal
      return params.sourceTextTotal;
    }
    // 检索结果模式：从 sourceTextHighlightRefs 获取连接的节点数
    return params.sourceTextHighlightRefs.current[params.matchId]
      ? params.sourceTextHighlightRefs.current[params.matchId].filter(
          (node) => node?.isConnected
        ).length
      : 0;
  }

  /**
   * 获取左侧润色出话的 occurrence 总数
   */
  getPolishedOutputOccurrenceCount(params: {
    matchId: string;
    polishedOutputViewerRef: React.MutableRefObject<import('../components/PlainTextViewer').PlainTextViewerHandle | null>;
    polishedOutputHighlightRanges: import('../types').HighlightRange[];
  }): number {
    if (this.context.isRecallInfoMode()) {
      // 召回信息模式：使用 polishedOutputHighlightRanges 计算总数
      return params.polishedOutputHighlightRanges.filter(
        (range) => range.matchId === params.matchId
      ).length;
    }
    // 检索结果模式：使用 polishedOutputViewerRef
    return (
      params.polishedOutputViewerRef.current?.getOccurrenceCount(params.matchId) ?? 0
    );
  }

  /**
   * 聚焦右侧文本（检索结果或召回信息）匹配
   */
  focusSourceTextMatch(params: {
    matchId: string;
    occurrenceIndex: number;
    recallInfoViewerRef: React.MutableRefObject<import('../components/PlainTextViewer').PlainTextViewerHandle | null>;
    focusSourceTextMatchFn: (matchId: string, occurrenceIndex: number) => void;
  }): void {
    if (this.context.isRecallInfoMode()) {
      // 召回信息模式：使用 recallInfoViewerRef 来聚焦右侧召回信息
      params.recallInfoViewerRef.current?.focusMatch(
        params.matchId,
        params.occurrenceIndex
      );
    } else {
      // 检索结果模式：使用 focusSourceTextMatch 来聚焦检索结果
      params.focusSourceTextMatchFn(params.matchId, params.occurrenceIndex);
    }
  }

  /**
   * 获取右侧文本（检索结果或召回信息）的总 occurrence 数量（基于 ranges）
   */
  getSourceTextTotalFromRanges(params: {
    matchId: string;
    recallInfoViewerRef: React.MutableRefObject<import('../components/PlainTextViewer').PlainTextViewerHandle | null>;
    sourceTextHighlightRanges: import('../types').HighlightRange[];
  }): number {
    if (this.context.isRecallInfoMode()) {
      // 召回信息模式：使用 recallInfoViewerRef 获取 occurrence 数量
      return (
        params.recallInfoViewerRef.current?.getOccurrenceCount(params.matchId) ??
        0
      );
    }
    // 检索结果模式：使用 sourceTextHighlightRanges 计算总数
    return params.sourceTextHighlightRanges.filter(
      (range) => range.matchId === params.matchId
    ).length;
  }
}

