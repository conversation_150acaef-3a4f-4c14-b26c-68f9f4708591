import type { Match } from '../types';

/**
 * 计算文本去除所有空白后的长度，用于判断长文本高亮阈值。
 */
export const getNormalizedLength = (value: string) => value.replace(/\s+/g, '').length;

/**
 * 按照用户指定的算法查找所有匹配：
 * 1. 从polishedOutput字符串的开头开始，取minLength长度的子串
 * 2. 在sourceText中查找是否存在相同的子串
 * 3. 如果存在，保持polishedOutput中的start不变，end逐个增加，直到匹配不上
 * 4. 记录此时的最长匹配文本
 * 5. 然后从此时的end开始，重复上述步骤
 */
const findAllMatchesByGreedy = (
  sourceText: string,
  polishedOutput: string,
  minLength: number,
  sourceTextOffset = 0,
  polishedOutputOffset = 0,
): Match[] => {
  const matches: Match[] = [];
  let polishedOutputIndex = 0;

  while (polishedOutputIndex <= polishedOutput.length - minLength) {
    // 从polishedOutput的当前位置开始，取minLength长度的子串
    const startSubstring = polishedOutput.slice(polishedOutputIndex, polishedOutputIndex + minLength);
    const normalizedStartLen = getNormalizedLength(startSubstring);
    
    // 如果去除空格后的长度不满足minLength，跳过
    if (normalizedStartLen < minLength) {
      polishedOutputIndex++;
      continue;
    }

    // 在sourceText中查找是否存在相同的子串（不限制位置）
    const sourceTextIndex = sourceText.indexOf(startSubstring);
    
    if (sourceTextIndex === -1) {
      // 如果找不到，继续下一个位置
      polishedOutputIndex++;
      continue;
    }

    // 找到了匹配，现在尝试扩展匹配长度
    let matchLength = minLength;
    let bestMatch: { sourceTextStart: number; polishedOutputStart: number; length: number } | null = null;

    // 保持polishedOutput中的start不变，end逐个增加，直到匹配不上
    while (polishedOutputIndex + matchLength <= polishedOutput.length) {
      const extendedSubstring = polishedOutput.slice(polishedOutputIndex, polishedOutputIndex + matchLength);
      const normalizedLen = getNormalizedLength(extendedSubstring);
      
      // 检查去除空格后的长度是否满足minLength
      if (normalizedLen < minLength) {
        matchLength++;
        continue;
      }

      // 在sourceText中查找扩展后的子串（不限制位置，找到最长匹配即可）
      const foundIndex = sourceText.indexOf(extendedSubstring);

      if (foundIndex === -1) {
        // 匹配不上，停止扩展
        break;
      }

      // 记录当前的最长匹配
      bestMatch = {
        sourceTextStart: foundIndex,
        polishedOutputStart: polishedOutputIndex,
        length: matchLength,
      };

      matchLength++;
    }

    // 如果找到了匹配，添加到结果中
    if (bestMatch) {
      const matchedText = polishedOutput.slice(bestMatch.polishedOutputStart, bestMatch.polishedOutputStart + bestMatch.length);
      matches.push({
        sourceText: {
          start: sourceTextOffset + bestMatch.sourceTextStart,
          end: sourceTextOffset + bestMatch.sourceTextStart + bestMatch.length,
        },
        polishedOutput: {
          start: polishedOutputOffset + bestMatch.polishedOutputStart,
          end: polishedOutputOffset + bestMatch.polishedOutputStart + bestMatch.length,
        },
        text: matchedText,
      });

      // 从匹配的end开始，继续查找下一个匹配
      polishedOutputIndex = bestMatch.polishedOutputStart + bestMatch.length;
    } else {
      // 如果没有找到匹配，继续下一个位置
      polishedOutputIndex++;
    }
  }

  return matches;
};

/**
 * 收集两段文本之间的所有匹配。
 * 使用用户指定的贪心算法：从polishedOutput开头开始，逐个位置尝试扩展匹配。
 * 计算时确保 minLength 至少为 4。
 */
const collectMatches = (
  sourceText: string,
  polishedOutput: string,
  minLength: number,
  sourceTextOffset = 0,
  polishedOutputOffset = 0,
): Match[] => {
  // 计算时确保 minLength 至少为 4
  const effectiveMinLength = Math.max(4, minLength);
  return findAllMatchesByGreedy(sourceText, polishedOutput, effectiveMinLength, sourceTextOffset, polishedOutputOffset);
};

/**
 * 基于最长公共子串集合，返回排序后的匹配列表，供长文本模式使用。
 * 注意：重叠过滤在 calculateScenarioHighlightRanges 中统一进行。
 */
export const findTextMatches = (
  sourceText: string,
  polishedOutput: string,
  minLength: number,
): Match[] => {
  return collectMatches(sourceText, polishedOutput, minLength);
};

/**
 * 查找子串在文本中的所有出现位置（允许重叠）。
 * 使用步长为1来确保找到所有匹配，包括重叠的匹配。
 */
export const findAllOccurrences = (haystack: string, needle: string) => {
  if (!needle.length) {
    return [];
  }
  const positions: number[] = [];
  let index = 0;
  // 使用步长为1，确保找到所有匹配（包括重叠的）
  while (index <= haystack.length - needle.length) {
    const found = haystack.indexOf(needle, index);
    if (found === -1) {
      break;
    }
    positions.push(found);
    // 步长为1，确保不遗漏任何匹配
    index = found + 1;
  }
  return positions;
};

