import type { SourceMode, MatchMode, ScenarioConfig } from './scenario';
import { getScenarioConfig } from './scenario';

/**
 * 场景上下文：提供场景相关的辅助方法
 */
export class ScenarioContext {
  private config: ScenarioConfig;

  constructor(matchMode: MatchMode, sourceMode: SourceMode) {
    this.config = getScenarioConfig(matchMode, sourceMode);
  }

  /**
   * 获取右侧文本（检索结果或召回信息）
   * - composedSourceText: 检索结果文本（来自 searchDataList）
   * - recallInfoText: 召回信息文本（来自 currentQuery）
   */
  getSourceText(params: {
    composedSourceText: string;
    recallInfoText: string;
  }): string {
    return this.config.getSourceText(params);
  }

  /**
   * 获取左侧润色出话文本（来自 output）
   * - composedPolishedOutputText: 润色出话文本（来自 output）
   */
  getPolishedOutputText(params: {
    composedPolishedOutputText: string;
    recallInfoText: string;
  }): string {
    return this.config.getPolishedOutputText(params);
  }

  /**
   * 是否为召回信息模式
   */
  isRecallInfoMode(): boolean {
    return this.config.sourceMode === 'recall-info';
  }

  /**
   * 是否为检索结果模式
   */
  isSearchResultMode(): boolean {
    return this.config.sourceMode === 'search-result';
  }

  /**
   * 是否为自由选择匹配模式
   */
  isFreeMode(): boolean {
    return this.config.matchMode === 'free';
  }

  /**
   * 获取场景key
   */
  getScenarioKey(): string {
    return `${this.config.matchMode}-${this.config.sourceMode}`;
  }
}

