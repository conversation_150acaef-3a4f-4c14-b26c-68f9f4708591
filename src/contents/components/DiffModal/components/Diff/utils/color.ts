/**
 * 生成更柔和的高亮颜色，适用于长文本/自由选择模式，避免页面色块过饱和。
 */
export const generateSoftColor = (index: number) =>
  `hsl(${(index * 137.508) % 360}, 60%, 92%)`;

/**
 * 将高亮颜色调整为更柔和的视觉效果。
 * 仅当输入为 HSL 颜色时才调整亮度，否则原样返回。
 * fallback 用于处理缺省颜色场景，保持视觉上的统一。
 */
export const lightenHighlightColor = (color?: string, fallback = '#e0f2fe') => {
  if (!color) {
    return fallback;
  }
  const hslMatch = color.match(
    /^hsl\(\s*([\d.]+)\s*,\s*([\d.]+)%\s*,\s*([\d.]+)%\s*\)$/i
  );
  if (!hslMatch) {
    return color;
  }
  const [, hue] = hslMatch;
  return `hsl(${hue}, 85%, 92%)`;
};

