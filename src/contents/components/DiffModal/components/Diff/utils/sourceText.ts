/** 描述在全文中的起止位置，start 为包含、end 为不包含。 */
import type {
  SourceTextBlockView,
  SourceTextComposition,
  SourceTextItemView,
  TraceData,
} from '../types';

type SourceTextBlockLike = {
  title?: string;
  resultList?: { title?: string; content?: string; pubTime?: string }[];
};

/**
 * 将原始数据结构转换为渲染友好的结构。
 * 包含扁平化的全文本以及每个区块/条目的相对位置。
 */
export const composeSourceTextContent = <T extends SourceTextBlockLike>(
  sourceTextBlocks: T[],
): SourceTextComposition => {
  let composedText = '';
  const blockViews: SourceTextBlockView[] = [];

  sourceTextBlocks.forEach((block, blockIndex) => {
    const trimmedTitle = block.title?.trim() ?? '';

    let blockText = '';
    let cursor = 0;
    const relativeItems: SourceTextItemView[] = [];
    const blockKeyBase =
      block.title ?? block.resultList?.[0]?.title ?? block.resultList?.[0]?.content ?? 'block';
    const blockKey = `${blockKeyBase}-${blockIndex}`;
    const blockView: SourceTextBlockView = {
      key: blockKey,
      items: [],
    };

    const append = (value: string) => {
      blockText += value;
      cursor += value.length;
    };

    if (trimmedTitle) {
      const start = cursor;
      append(trimmedTitle);
      blockView.title = {
        text: trimmedTitle,
        range: { start, end: cursor },
      };
    }

    let hasItems = false;
    block.resultList?.forEach((item, itemIndex) => {
      const trimmedItemTitle = item.title?.trim() ?? '';
      const trimmedContent = item.content?.trim() ?? '';
      if (!trimmedItemTitle && !trimmedContent) {
        return;
      }

      if ((blockView.title && !hasItems) || hasItems) {
        append('\n\n');
      }

      const itemKeyBase = item.title ?? item.content ?? 'item';
      const itemKey = `${itemKeyBase}-${itemIndex}`;
      const relativeItem: SourceTextItemView = {
        key: itemKey,
      };

      if (trimmedItemTitle) {
        const prefixedTitle = trimmedItemTitle;
        const start = cursor;
        append(prefixedTitle);
        relativeItem.title = {
          text: prefixedTitle,
          range: { start, end: cursor },
          pubTime: item.pubTime,
        };
        if (trimmedContent) {
          append('\n');
        }
      }

      if (trimmedContent) {
        const start = cursor;
        append(trimmedContent);
        relativeItem.content = {
          text: trimmedContent,
          range: { start, end: cursor },
          pubTime: item.pubTime,
        };
      }

      relativeItems.push(relativeItem);
      hasItems = true;
    });

    if (!blockText) {
      return;
    }

    if (composedText) {
      composedText += '\n\n\n';
    }

    const blockStart = composedText.length;
    composedText += blockText;

    if (blockView.title?.range) {
      blockView.title = {
        text: blockView.title.text,
        range: {
          start: blockStart + blockView.title.range.start,
          end: blockStart + blockView.title.range.end,
        },
      };
    }

    blockView.items = relativeItems.map((item) => {
      const nextItem: SourceTextItemView = { key: item.key };
      if (item.title?.range) {
        nextItem.title = {
          text: item.title.text,
          range: item.title.range
            ? {
                start: blockStart + item.title.range.start,
                end: blockStart + item.title.range.end,
              }
            : undefined,
          pubTime: item.title.pubTime,
        };
      }
      if (item.content?.range) {
        nextItem.content = {
          text: item.content.text,
          range: item.content.range
            ? {
                start: blockStart + item.content.range.start,
                end: blockStart + item.content.range.end,
              }
            : undefined,
          pubTime: item.content.pubTime,
        };
      }
      return nextItem;
    });

    blockViews.push(blockView);
  });

  return { text: composedText, blocks: blockViews };
};

export type TraceDataMappingResult = {
  sourceTextList: {
    title: string;
    resultList: { title?: string; content: string; pubTime?: string }[];
  }[];
  polishedOutputText: string;
  recallInfoText: string;
};

/**
 * 将 Trace 数据的原始字段转换为组件可直接消费的结构。
 * 返回值包含：
 * - sourceTextList：检索结果分块（标题 + resultList，来自 searchDataList）
 * - polishedOutputText：润色出话文本（来自 output）
 * - recallInfoText：召回信息文本（来自 currentQuery）
 */
export function mapTraceData(data: TraceData | undefined): TraceDataMappingResult {
  if (!data) {
    return {
      sourceTextList: [],
      polishedOutputText: '',
      recallInfoText: '',
    };
  }
  return {
    sourceTextList:
      data.searchDataList?.map((item) => ({
        title: `${item.type}-${item.query}`,
        resultList:
          typeof item.result === 'string'
            ? [{ content: item.result }]
            : item.result.map((subItem) => ({
                title: `${subItem?.['文章来源']} | ${subItem?.['文章标题']}`,
                content: subItem['文章片段内容'],
                pubTime: subItem['文章发布时间'],
              })),
      })) ?? [],
    polishedOutputText: data.output ?? '',
    recallInfoText: data.currentQuery ?? '',
  };
}
