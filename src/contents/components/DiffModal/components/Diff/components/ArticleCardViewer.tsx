import type { ReactNode } from 'react';

import type { SourceTextBlockView, SourceTextItemView, TextRange } from '../types';
import styles from '../index.module.less';

/**
 * ArticleCardViewer 属性：
 * - blocks: 经过 `composeSourceTextContent` 处理后的检索结果区块结构。
 * - renderSegments: 由父组件传入的渲染函数，用于接入高亮逻辑。
 */
type ArticleCardViewerProps = {
  blocks: SourceTextBlockView[];
  renderSegments: (text: string, range: TextRange | undefined, keyPrefix: string) => ReactNode;
};

/**
 * 多级文章卡片查看器，用于展示检索结果的多级结构（区块-标题-内容）。
 * 组件不关心高亮状态本身，仅负责将结构化文本与渲染函数组合输出。
 */
const ArticleCardViewer = ({ blocks, renderSegments }: ArticleCardViewerProps) => {
  return (
    <div className={styles.sourceTextCardStack}>
      {blocks.map((block, blockIndex) => (
        <section 
          key={block.key ?? `block-${blockIndex}`} 
          className={styles.sourceTextCard}
          data-source-text-block-index={blockIndex}
        >
          {block.title?.text && (
            <header className={styles.sourceTextCardHeader} style={{ whiteSpace: 'pre-wrap' }}>
              {renderSegments(
                block.title.text,
                block.title.range,
                `source-text-block-${blockIndex}-title`,
              )}
              {block.title?.pubTime && (
                <time className={styles.sourceTextCardMeta}>{block.title.pubTime}</time>
              )}
            </header>
          )}
          {block.items.length > 0 && (
            <div className={styles.sourceTextCardBody}>
              {block.items.map((item: SourceTextItemView, itemIndex: number) => (
                <article 
                  key={item.key ?? `item-${itemIndex}`} 
                  className={styles.sourceTextResult}
                  data-source-text-block-index={blockIndex}
                  data-source-text-item-index={itemIndex}
                >
                  {item.title?.text && (
                    <h4 className={styles.sourceTextResultTitle} style={{ whiteSpace: 'pre-wrap' }}>
                      <span className={styles.sourceTextResultTitleText}>
                        {renderSegments(
                          item.title.text,
                          item.title.range,
                          `source-text-block-${blockIndex}-item-${itemIndex}-title`,
                        )}
                      </span>
                      {item.title?.pubTime && (
                        <time className={styles.sourceTextResultMeta}>{item.title.pubTime}</time>
                      )}
                    </h4>
                  )}
                  {item.content?.text && (
                    <p className={styles.sourceTextResultContent}>
                      {renderSegments(
                        item.content.text,
                        item.content.range,
                        `source-text-block-${blockIndex}-item-${itemIndex}-content`,
                      )}
                    </p>
                  )}
                  {!item.title?.pubTime && item.content?.pubTime && (
                    <time className={styles.sourceTextResultMeta}>{item.content.pubTime}</time>
                  )}
                </article>
              ))}
            </div>
          )}
        </section>
      ))}
    </div>
  );
};

export default ArticleCardViewer;

