import classNames from 'classnames';
import type { HighlightMode } from '../types';
import type { ActiveOccurrence } from './PlainTextViewer';
import type { PlainTextViewerHandle } from './PlainTextViewer';
import DiffToolbar from './DiffToolbar';
import ArticleCardViewer from './ArticleCardViewer';
import PlainTextViewer from './PlainTextViewer';
import styles from '../index.module.less';

interface DiffViewProps {
  mode: HighlightMode;
  loading: boolean;
  error: boolean;
  minLength: number;
  freeMode: 'pick' | 'read';
  hasFreeSelections: boolean;
  rightViewMode: 'search-result' | 'recall-info';
  sourceTextViewBlocks: import('../types').SourceTextBlockView[];
  composedPolishedOutputText: string;
  recallInfoText: string;
  polishedOutputHighlightRanges: import('../types').HighlightRange[];
  recallInfoHighlightRanges: {
    sourceTextRanges: import('../types').HighlightRange[];
    polishedOutputRanges: import('../types').HighlightRange[];
  };
  selectedMatchId: string | undefined;
  sourceTextActiveOccurrence: ActiveOccurrence | undefined;
  polishedOutputActiveOccurrence: ActiveOccurrence | undefined;
  sourceTextOccurrenceSummary: { current: number; total: number; text: string } | undefined;
  recallInfoOccurrenceSummary: { current: number; total: number; text: string } | undefined;
  enablePolishedOutputSelection: boolean;
  polishedOutputViewerRef: React.RefObject<PlainTextViewerHandle | null>;
  recallInfoViewerRef: React.RefObject<PlainTextViewerHandle | null>;
  renderSegments: (
    text: string,
    range: import('../types').TextRange | undefined,
    keyPrefix: string
  ) => React.ReactNode;
  onModeChange: (mode: HighlightMode) => void;
  onMinLengthChange: (value: number) => void;
  onFreeModeChange: (mode: 'pick' | 'read') => void;
  onFreeClear: () => void;
  onPolishedOutputSelection: (selection?: import('../types').FreeSelectionPayload) => void;
  onMatchSelect: (matchId: string, source: 'sourceText' | 'polishedOutput', occurrenceIndex?: number) => void;
  onRightViewModeChange: (mode: 'search-result' | 'recall-info') => void;
  isEmpty?: boolean;
  viewModeState?: {
    isRecallInfoDisabled: boolean;
    isSearchResultDisabled: boolean;
    bothEmpty?: boolean;
    shouldCheckRecallInfo: (currentMode: 'search-result' | 'recall-info') => boolean;
    shouldCheckSearchResult: (currentMode: 'search-result' | 'recall-info') => boolean;
  };
}

/**
 * Diff 视图组件，负责渲染 UI
 */
export const DiffView = (props: DiffViewProps) => {
  const {
    mode,
    loading,
    error,
    minLength,
    freeMode,
    hasFreeSelections,
    rightViewMode,
    sourceTextViewBlocks,
    composedPolishedOutputText,
    recallInfoText,
    polishedOutputHighlightRanges,
    recallInfoHighlightRanges,
    selectedMatchId,
    sourceTextActiveOccurrence,
    polishedOutputActiveOccurrence,
    sourceTextOccurrenceSummary,
    recallInfoOccurrenceSummary,
    enablePolishedOutputSelection,
    polishedOutputViewerRef,
    recallInfoViewerRef,
    renderSegments,
    onModeChange,
    onMinLengthChange,
    onFreeModeChange,
    onFreeClear,
    onPolishedOutputSelection,
    onMatchSelect,
    onRightViewModeChange,
    isEmpty = false,
    viewModeState,
  } = props;

  // 如果 viewModeState 不存在，提供默认值（向后兼容）
  const {
    isRecallInfoDisabled = false,
    isSearchResultDisabled = false,
    shouldCheckRecallInfo = (mode: 'search-result' | 'recall-info') => mode === 'recall-info',
    shouldCheckSearchResult = (mode: 'search-result' | 'recall-info') => mode === 'search-result',
  } = viewModeState ?? {};

  return (
    <div className={styles.diffPage}>
      <div>
        <DiffToolbar
          mode={mode}
          minLength={minLength}
          freeMode={freeMode}
          hasFreeSelections={hasFreeSelections}
          onModeChange={onModeChange}
          onMinLengthChange={onMinLengthChange}
          onFreeModeChange={onFreeModeChange}
          onFreeClear={onFreeClear}
        />
        <div className={styles.viewer}>
          {loading ? (
            <div className={styles.errorState}>正在加载中...</div>
          ) : error ? (
            <div className={styles.errorState}>
              <div className={styles.errorIcon}>⚠️</div>
              <h3 className={styles.errorTitle}>内容加载失败</h3>
              <p className={styles.errorDescription}>
                我们暂时无法获取对比内容，可能是网络波动或服务异常。请稍后重试或刷新页面。
              </p>
            </div>
          ) : isEmpty ? (
            <div className={styles.errorState}>
              <div className={styles.errorIcon}>📄</div>
              <h3 className={styles.errorTitle}>暂无数据</h3>
              <p className={styles.errorDescription}>
                当前没有可对比的内容数据，请检查数据源。
              </p>
            </div>
          ) : (
            <div className={styles.viewerContent}>
              <div className={styles.viewerHighlight}>
                <section className={styles.card}>
                  <h3 className={styles.cardHeader}>润色出话</h3>
                  <PlainTextViewer
                    ref={polishedOutputViewerRef}
                    text={composedPolishedOutputText}
                    ranges={polishedOutputHighlightRanges}
                    selectedMatchId={selectedMatchId}
                    activeOccurrence={polishedOutputActiveOccurrence}
                    onMatchSelect={(matchId, occurrenceIndex) =>
                      onMatchSelect(matchId, 'polishedOutput', occurrenceIndex)
                    }
                    enableSelection={enablePolishedOutputSelection}
                    onSelection={onPolishedOutputSelection}
                    highlightMode={mode}
                  />
                </section>
              </div>
              <div className={styles.viewerHighlight}>
                <section className={styles.card}>
                  <div className={styles.cardHeader}>
                    <h3>来源</h3>
                    <div className={styles.viewModeToggle}>
                      <label
                        className={classNames(styles.toggleOption, {
                          [styles.activeToggleOption]: shouldCheckRecallInfo(rightViewMode),
                          [styles.disabledToggleOption]: isRecallInfoDisabled,
                        })}
                      >
                        <input
                          type="radio"
                          name="rightViewMode"
                          value="recall-info"
                          checked={shouldCheckRecallInfo(rightViewMode)}
                          disabled={isRecallInfoDisabled}
                          onChange={(e) =>
                            onRightViewModeChange(
                              e.target.value as 'search-result' | 'recall-info'
                            )
                          }
                        />
                        <span>召回信息</span>
                      </label>
                      <label
                        className={classNames(styles.toggleOption, {
                          [styles.activeToggleOption]: shouldCheckSearchResult(rightViewMode),
                          [styles.disabledToggleOption]: isSearchResultDisabled,
                        })}
                      >
                        <input
                          type="radio"
                          name="rightViewMode"
                          value="search-result"
                          checked={shouldCheckSearchResult(rightViewMode)}
                          disabled={isSearchResultDisabled}
                          onChange={(e) =>
                            onRightViewModeChange(
                              e.target.value as 'search-result' | 'recall-info'
                            )
                          }
                        />
                        <span>检索结果</span>
                      </label>
                    </div>
                  </div>
                  {viewModeState?.bothEmpty ? (
                    <div className={styles.cardBody}>
                      <div className={styles.errorState} style={{ padding: '40px 20px' }}>
                        <div className={styles.errorIcon}>📄</div>
                        <h3 className={styles.errorTitle}>暂无数据</h3>
                        <p className={styles.errorDescription}>
                          检索结果和召回信息均为空，请检查数据源。
                        </p>
                      </div>
                    </div>
                  ) : rightViewMode === 'search-result' ? (
                    <>
                      {sourceTextOccurrenceSummary ? (
                        <div className={styles.matchIndicator}>
                          当前第 {sourceTextOccurrenceSummary.current}/
                          {sourceTextOccurrenceSummary.total} 个 （匹配内容：
                          {sourceTextOccurrenceSummary.text || '无'}）
                        </div>
                      ) : null}
                      <div className={styles.cardBody}>
                        <ArticleCardViewer
                          blocks={sourceTextViewBlocks}
                          renderSegments={renderSegments}
                        />
                      </div>
                    </>
                  ) : (
                    <>
                      {recallInfoOccurrenceSummary ? (
                        <div className={styles.matchIndicator}>
                          当前第 {recallInfoOccurrenceSummary.current}/
                          {recallInfoOccurrenceSummary.total} 个 （匹配内容：
                          {recallInfoOccurrenceSummary.text || '无'}）
                        </div>
                      ) : null}
                      <div className={styles.cardBody}>
                        <PlainTextViewer
                          ref={recallInfoViewerRef}
                          text={recallInfoText}
                          ranges={
                            rightViewMode === 'recall-info'
                              ? recallInfoHighlightRanges.sourceTextRanges
                              : []
                          }
                          selectedMatchId={selectedMatchId}
                          activeOccurrence={
                            rightViewMode === 'recall-info'
                              ? sourceTextActiveOccurrence
                              : polishedOutputActiveOccurrence
                          }
                          onMatchSelect={(matchId, occurrenceIndex) =>
                            // 在召回信息模式下，右侧召回信息（currentQuery）与左侧润色出话（output）进行匹配
                            onMatchSelect(matchId, 'sourceText', occurrenceIndex)
                          }
                          highlightMode={mode}
                        />
                      </div>
                    </>
                  )}
                </section>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

