import { type ChangeEvent } from 'react';

import classNames from 'classnames';

import type { FreeSelectionMode, HighlightMode } from '../types';
import styles from '../index.module.less';

const MODE_HINT: Record<HighlightMode, string> = {
  numbers: '高亮并联动检索结果与润色出话中的相同数值、日期、百分比等内容。在左侧点击数字后，右侧检索结果中显示高亮。',
  text: '高亮显示检索结果与润色出话中相同的长段文本。点击色块可跳转至对应位置。',
  free: '在润色出话中使用光标选中一段文本，将在右侧检索结果中匹配高亮显示。点击色块可跳转至对应位置。',
};

/**
 * Diff 工具栏属性定义：
 * - mode: 当前高亮模式
 * - minLength: 长文本模式下的最小匹配长度
 * - freeMode: 自由选择子模式（点选/只读）
 * - hasFreeSelections: 是否存在已选自由分组，用于控制清除按钮状态
 * - onModeChange/onMinLengthChange/onFreeModeChange/onFreeClear: 外部状态更新回调
 */
type DiffToolbarProps = {
  mode: HighlightMode;
  minLength: number;
  freeMode: FreeSelectionMode;
  hasFreeSelections: boolean;
  onModeChange: (mode: HighlightMode) => void;
  onMinLengthChange: (value: number) => void;
  onFreeModeChange: (mode: FreeSelectionMode) => void;
  onFreeClear: () => void;
};

/**
 * DiffToolbar 负责展示顶部控制面板，拆解出模式切换、阈值输入以及自由选择相关操作。
 * 所有状态均由父组件托管，这里仅负责触发相应回调。
 */
const DiffToolbar = ({
  mode,
  minLength,
  freeMode,
  hasFreeSelections,
  onModeChange,
  onMinLengthChange,
  onFreeModeChange,
  onFreeClear,
}: DiffToolbarProps) => {
  /** 捕获模式单选框的变更，并透传给父组件。 */
  const handleModeChange = (event: ChangeEvent<HTMLInputElement>) => {
    onModeChange(event.target.value as HighlightMode);
  };

  /** 针对数值输入框的变更进行归一化。 */
  const handleMinLengthChange = (event: ChangeEvent<HTMLInputElement>) => {
    const value = Number(event.target.value);
    onMinLengthChange(Number.isNaN(value) ? 0 : value);
  };

  /** 切换自由选择子模式（点选/只读）。 */
  const handleFreeModeChange = (event: ChangeEvent<HTMLInputElement>) => {
    onFreeModeChange(event.target.value as FreeSelectionMode);
  };

  return (
    <div className={styles.toolbar}>
      <div className={styles.modePanel}>
        <div className={styles.modeGroup}>
          <div className={styles.modeOptions}>
            <label
              className={classNames(styles.radioOption, {
                [styles.activeOption]: mode === 'text',
              })}
            >
              <input
                type="radio"
                name="highlightMode"
                value="text"
                checked={mode === 'text'}
                onChange={handleModeChange}
              />
              <span>长段文本匹配</span>
            </label>
            <label
              className={classNames(styles.radioOption, {
                [styles.activeOption]: mode === 'numbers',
              })}
            >
              <input
                type="radio"
                name="highlightMode"
                value="numbers"
                checked={mode === 'numbers'}
                onChange={handleModeChange}
              />
              <span>数字匹配</span>
            </label>
            <label
              className={classNames(styles.radioOption, {
                [styles.activeOption]: mode === 'free',
              })}
            >
              <input
                type="radio"
                name="highlightMode"
                value="free"
                checked={mode === 'free'}
                onChange={handleModeChange}
              />
              <span>自由选中</span>
            </label>
          </div>
          <p className={styles.modeHint}>{MODE_HINT[mode]}</p>
        </div>
        {mode === 'text' && (
          <label className={styles.inputWrapper}>
            <span className={styles.labelText}>最短匹配长度：</span>
            <input
              type="number"
              value={minLength}
              onChange={handleMinLengthChange}
              className={styles.numberInput}
            />
          </label>
        )}
        {mode === 'free' && (
          <div className={styles.subModeContainer}>
            <div className={styles.subModeGroup}>
              <label
                className={classNames(styles.radioOption, {
                  [styles.activeOption]: freeMode === 'pick',
                })}
              >
                <input
                  type="radio"
                  name="freeMode"
                  value="pick"
                  checked={freeMode === 'pick'}
                  onChange={handleFreeModeChange}
                />
                <span>点选</span>
              </label>
              <label
                className={classNames(styles.radioOption, {
                  [styles.activeOption]: freeMode === 'read',
                })}
              >
                <input
                  type="radio"
                  name="freeMode"
                  value="read"
                  checked={freeMode === 'read'}
                  onChange={handleFreeModeChange}
                />
                <span>只读</span>
              </label>
            </div>
            <button
              type="button"
              className={styles.clearButton}
              onClick={onFreeClear}
              disabled={!hasFreeSelections}
            >
              清除所有已选
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default DiffToolbar;

