import { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';

import { buildSegments, lightenHighlightColor } from '../utils';
import type { HighlightRange, HighlightMode } from '../types';
import styles from '../index.module.less';

export type ActiveOccurrence = {
  matchId: string;
  occurrenceIndex: number;
};

export type PlainTextViewerHandle = {
  focusMatch: (matchId: string, occurrenceIndex?: number) => void;
  getOccurrenceCount: (matchId: string) => number;
};

/**
 * PlainTextViewer 属性定义：
 * - text/ranges：纯文本及其高亮范围集合。
 * - selectedMatchId/activeOccurrence：外部传入的选中状态，用于同步联动。
 * - onMatchSelect：点击高亮时触发，交由父组件处理。
 * - enableSelection/onSelection：自由选择模式下的选区回调。
 * - highlightMode：用于区分数字模式的特殊 hover/颜色逻辑。
 */
type PlainTextViewerProps = {
  text: string;
  ranges: HighlightRange[];
  selectedMatchId?: string;
  onMatchSelect?: (matchId: string, occurrenceIndex: number) => void;
  enableSelection?: boolean;
  onSelection?: (selection?: { text: string; start: number; end: number }) => void;
  highlightMode?: HighlightMode;
  activeOccurrence?: ActiveOccurrence;
};

/**
 * 纯文本查看器，负责渲染纯文本的高亮效果，并提供 imperative handle 实现外部聚焦。
 * 组件内部会缓存每组 matchId 对应的 DOM 节点，方便跨区域跳转。
 */
const PlainTextViewer = forwardRef<PlainTextViewerHandle, PlainTextViewerProps>(
  (
    {
      text,
      ranges,
      selectedMatchId,
      onMatchSelect,
      enableSelection = false,
      onSelection,
      highlightMode,
      activeOccurrence,
    },
    ref,
  ) => {
    const textRefs = useRef<Record<string, HTMLElement[]>>({});
    const textContainerRef = useRef<HTMLDivElement | null>(null);
    const [hoveredMatchId, setHoveredMatchId] = useState<string>();

    const isNumbersMode = highlightMode === 'numbers';
    const useSoftColor = highlightMode === 'numbers' || highlightMode === 'free';

    /** 若当前非数字模式，则不展示 hover 态，保持 UI 一致性。 */
    useEffect(() => {
      if (!isNumbersMode && hoveredMatchId) {
        setHoveredMatchId(undefined);
      }
    }, [isNumbersMode, hoveredMatchId]);

    const textSegments = useMemo(
      () => buildSegments(text, ranges, 'polishedOutput'),
      [ranges, text],
    );

    /** 对外暴露的滚动聚焦实现，确保可稳定定位到指定 occurrence。 */
    const focusMatch = useCallback((matchId: string, occurrenceIndex = 0) => {
      const nodes = textRefs.current[matchId];
      if (!nodes || !nodes.length) {
        return;
      }
      const connected = nodes.filter((node) => node?.isConnected);
      if (!connected.length) {
        delete textRefs.current[matchId];
        return;
      }
      textRefs.current[matchId] = connected;
      const element = connected[occurrenceIndex] ?? connected[0];
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      element?.scrollIntoView({ behavior: 'instant' as any, block: 'center' });
      element?.focus?.({ preventScroll: true });
    }, []);

    /** 返回指定 matchId 当前仍挂载在 DOM 上的 occurrence 数量。 */
    const getOccurrenceCount = useCallback(
      (matchId: string) => textRefs.current[matchId]?.filter((node) => node?.isConnected).length ?? 0,
      [],
    );

    useImperativeHandle(
      ref,
      () => ({
        focusMatch,
        getOccurrenceCount,
      }),
      [focusMatch, getOccurrenceCount],
    );

    /**
     * 将高亮节点的引用缓存在 textRefs 中，卸载时同步清理。
     * 该逻辑与 `Diff` 中的 register 函数类似，用于保证 ref 数据始终可用。
     */
    const registerHighlightRef = useCallback(
      (matchId: string | undefined, element: HTMLElement | null, occurrenceIndex = 0) => {
        if (!matchId) {
          return;
        }
        if (element) {
          if (!textRefs.current[matchId]) {
            textRefs.current[matchId] = [];
          }
          textRefs.current[matchId][occurrenceIndex] = element;
          return;
        }
        const list = textRefs.current[matchId];
        if (!list) {
          return;
        }
        const filtered = list.filter((node) => node?.isConnected) as HTMLElement[];
        if (filtered.length) {
          textRefs.current[matchId] = filtered;
        } else {
          delete textRefs.current[matchId];
        }
      },
      [],
    );

    /**
     * 响应点击/键盘选择文本区域高亮：
     * - 通知父组件当前 occurrence
     * - 立即调用 `focusMatch`，避免点击后失焦
     */
    const handleHighlightSelection = useCallback(
      (matchId: string | undefined, occurrenceIndex = 0) => {
        if (!matchId) {
          return;
        }
        onMatchSelect?.(matchId, occurrenceIndex);
      },
      [onMatchSelect],
    );

    /**
     * 自由选择模式下处理浏览器 selection：
     * - 限定仅在文本容器内生效
     * - 将选中文本与相对起止位置反馈给父组件
     */
    const handleTextSelection = useCallback(() => {
      if (!enableSelection || !textContainerRef.current) {
        return;
      }

      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) {
        return;
      }

      const range = selection.getRangeAt(0);
      if (!textContainerRef.current.contains(range.commonAncestorContainer)) {
        return;
      }

      const selectedText = selection.toString();
      if (!selectedText) {
        onSelection?.(undefined);
        return;
      }

      const preRange = range.cloneRange();
      preRange.selectNodeContents(textContainerRef.current);
      preRange.setEnd(range.startContainer, range.startOffset);
      const start = preRange.toString().length;
      const end = start + selectedText.length;

      onSelection?.({ text: selectedText, start, end });
    }, [enableSelection, onSelection]);

    const occurrenceCounter: Record<string, number> = {};

    return (
      <div
        ref={textContainerRef}
        className={styles.cardBody}
        style={{ whiteSpace: 'pre-wrap', lineHeight: 1.6 }}
        onMouseUp={enableSelection ? handleTextSelection : undefined}
        onKeyUp={enableSelection ? handleTextSelection : undefined}
      >
        {textSegments.map((segment) => {
          if (!segment.text) {
            return null;
          }
          if (!segment.highlight) {
            return (
              <span key={segment.id} style={{ display: 'inline' }}>
                {segment.text}
              </span>
            );
          }
          const matchId = segment.matchId ?? '';
          const occurrenceIndex = occurrenceCounter[matchId] ?? 0;
          occurrenceCounter[matchId] = occurrenceIndex + 1;
          const isActiveMatch = selectedMatchId === matchId;
          const isFocusedOccurrence =
            activeOccurrence?.matchId === matchId &&
            activeOccurrence?.occurrenceIndex === occurrenceIndex;
          const isHovered = hoveredMatchId === matchId;

          const inactiveBackground = 'rgb(223, 222, 247)';
          // 数字匹配模式下，鼠标悬浮颜色（基于底色稍微深一点）
          const hoverBackground = 'rgb(200, 195, 245)';
          // 数字匹配模式下，点击后的激活颜色
          const activeNumberBackground = 'rgb(228, 247, 222)';
          const defaultBackground = useSoftColor
            ? lightenHighlightColor(segment.color)
            : (segment.color ?? '#fef3c7');

          let backgroundColor = defaultBackground;
          if (highlightMode === 'numbers') {
            backgroundColor = isActiveMatch
              ? activeNumberBackground
              : isHovered
                ? hoverBackground
                : inactiveBackground;
          } else if (highlightMode === 'free') {
            backgroundColor = defaultBackground;
          }

          return (
            <mark
              key={segment.id}
              ref={(element) => registerHighlightRef(segment.matchId, element, occurrenceIndex)}
              data-match-id={matchId}
              data-match-index={occurrenceIndex}
              tabIndex={0}
              onClick={() => handleHighlightSelection(segment.matchId, occurrenceIndex)}
              onMouseDown={(event) => {
                event.preventDefault();
              }}
              onKeyDown={(event) => {
                if (event.key === 'Enter' || event.key === ' ') {
                  event.preventDefault();
                  handleHighlightSelection(segment.matchId, occurrenceIndex);
                }
              }}
              onMouseEnter={
                isNumbersMode ? () => setHoveredMatchId(matchId) : undefined
              }
              onMouseLeave={
                isNumbersMode
                  ? () => {
                      setHoveredMatchId((current) => (current === matchId ? undefined : current));
                    }
                  : undefined
              }
              style={{
                backgroundColor,
                display: 'inline',
                cursor: 'pointer',
                outline:
                  isFocusedOccurrence ||
                  (!activeOccurrence && selectedMatchId && segment.matchId === selectedMatchId)
                    ? '2px solid rgba(0, 0, 0, 0.4)'
                    : 'none',
                borderRadius: 4,
                padding: highlightMode === 'numbers' ? '0 2px' : undefined,
                transition: 'background-color 0.2s ease, outline 0.2s ease',
              }}
            >
              {segment.text}
            </mark>
          );
        })}
      </div>
    );
  },
);

PlainTextViewer.displayName = 'PlainTextViewer';

export default PlainTextViewer;

