/**
 * Diff 模块支持的高亮模式：
 * - numbers: 数值或日期多对多匹配
 * - text: 长文本最长公共子串匹配
 * - free: 自由选择模式
 */
export type HighlightMode = 'numbers' | 'text' | 'free';

/**
 * Diff 过程中记录的匹配结果。
 * sourceText/polishedOutput 分别指向右侧文本（检索结果或召回信息）与左侧润色出话中匹配片段的起止位置；text 保留匹配的原始文本。
 */
export type Match = {
  /** 右侧文本（检索结果或召回信息）中匹配片段的下标范围（包含 start，不包含 end） */
  sourceText: { start: number; end: number };
  /** 左侧润色出话中匹配片段的下标范围（包含 start，不包含 end） */
  polishedOutput: { start: number; end: number };
  /** 实际被匹配的文本内容 */
  text: string;
};

/**
 * 用于渲染的片段结构。highlight 标识是否需要高亮，color/matchId 用于视觉联动。
 */
export type Segment = {
  /** 片段唯一标识，方便 React diff */
  id: string;
  /** 展示用文本内容 */
  text: string;
  /** 是否高亮显示 */
  highlight: boolean;
  /** 片段对应的高亮颜色（仅对 highlight=true 的片段生效） */
  color?: string;
  /** 片段所属的匹配分组 id（用于点击联动） */
  matchId?: string;
  /** 该片段在同一 matchId 下的出现序号（从 0 开始），用于聚焦联动 */
  occurrenceIndex?: number;
};

/**
 * 高亮范围结构，描述某个匹配分组在文本中的位置、颜色以及 matchId。
 */
export type HighlightRange = {
  /** 范围起始下标（包含） */
  start: number;
  /** 范围结束下标（不包含） */
  end: number;
  /** 同一组高亮共享的分组 id */
  matchId: string;
  /** 分组对应的高亮颜色 */
  color: string;
};

/** 描述在全文中的起止位置，start 为包含、end 为不包含。 */
export type TextRange = { start: number; end: number };

/** 检索结果项结构，包含标题和正文的文本及范围映射。 */
export type SourceTextItemView = {
  key: string;
  title?: { text: string; range?: TextRange; pubTime?: string };
  content?: { text: string; range?: TextRange; pubTime?: string };
};

/** 检索结果区块结构，保留标题以及所有结果项。 */
export type SourceTextBlockView = {
  key: string;
  title?: { text: string; range?: TextRange; pubTime?: string };
  items: SourceTextItemView[];
};

/** 检索结果组合结果，包含拼接后的全文以及分块详情。 */
export type SourceTextComposition = {
  text: string;
  blocks: SourceTextBlockView[];
};

/** 自由选择模式下的操作方式：pick 为点选高亮，read 为只读查看。 */
export type FreeSelectionMode = 'pick' | 'read';

/** 自由选择操作产生的基础选区数据。 */
export type FreeSelectionPayload = {
  text: string;
  start: number;
  end: number;
};

/** 自由选择生成的高亮分组信息。 */
export type SelectionGroup = {
  matchId: string;
  color: string;
  text: string;
  anchor: { start: number; end: number };
  sourceTextRanges: HighlightRange[];
  polishedOutputRanges: HighlightRange[];
};

/** Trace 接口返回的结构化数据类型定义。 */
export type TraceHistoryItem = {
  answer: string;
  query: string;
  selectStockDsl?: string;
  class: string;
};

export type TraceSearchDataResult =
  | string
  | {
      文章片段内容: string;
      文章来源: string;
      文章类型?: string;
      文章标题: string;
      文章发布时间?: string;
      URL?: string | null;
      [key: string]: string | undefined | null;
    }[];

export type TraceSearchData = {
  type: string;
  query: string;
  result: TraceSearchDataResult;
};

export type TraceData = {
  historyList?: TraceHistoryItem[];
  userInput?: string;
  evaluationRemark?: string;
  output?: string;
  searchDataList?: TraceSearchData[];
  currentQuery?: string;
  references?: unknown[];
  userFeedback?: unknown;
  allInfoResult?: string[];
  CoT?: string;
  toolset?: Record<string, unknown>;
};