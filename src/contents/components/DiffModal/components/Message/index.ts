import './index.less';

type MessageType = 'info' | 'success' | 'error' | 'warning';

const DEFAULT_DURATION = 3000;

let messageContainer: HTMLDivElement | null = null;

const ensureContainer = () => {
  if (messageContainer || typeof window === 'undefined' || typeof document === 'undefined') {
    return messageContainer;
  }

  const container = document.createElement('div');
  container.className = 'app-message-container';
  document.body.appendChild(container);
  messageContainer = container;
  return messageContainer;
};

const createMessageElement = (content: string, type: MessageType) => {
  const element = document.createElement('div');
  element.className = `app-message app-message-${type}`;
  element.textContent = content;
  return element;
};

export const showMessage = (content: string, type: MessageType = 'info', duration = DEFAULT_DURATION) => {
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    console[type === 'error' ? 'error' : 'warn'](content);
    return;
  }

  const container = ensureContainer();
  if (!container) {
    return;
  }

  const messageElement = createMessageElement(content, type);
  container.appendChild(messageElement);

  // Trigger enter animation
  requestAnimationFrame(() => {
    messageElement.classList.add('app-message-enter');
  });

  const remove = () => {
    messageElement.classList.remove('app-message-enter');
    messageElement.classList.add('app-message-leave');
    messageElement.addEventListener(
      'transitionend',
      () => {
        container.removeChild(messageElement);
        if (!container.childElementCount) {
          container.remove();
          messageContainer = null;
        }
      },
      { once: true },
    );
  };

  const timer = window.setTimeout(remove, duration);

  messageElement.addEventListener(
    'click',
    () => {
      window.clearTimeout(timer);
      remove();
    },
    { once: true },
  );
};

export const showWarning = (content: string, duration = DEFAULT_DURATION) =>
  showMessage(content, 'warning', duration);

export const showError = (content: string, duration = DEFAULT_DURATION) =>
  showMessage(content, 'error', duration);

export const showSuccess = (content: string, duration = DEFAULT_DURATION) =>
  showMessage(content, 'success', duration);


