.app-message-container {
  position: fixed;
  top: 24px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 9999;
  pointer-events: none;
}

.app-message {
  min-width: 200px;
  max-width: 480px;
  padding: 12px 16px;
  border-radius: 4px;
  background: #fff;
  color: rgba(0, 0, 0, 0.88);
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.06);
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
  pointer-events: auto;
}

.app-message-enter {
  opacity: 1;
  transform: translateY(0);
}

.app-message-leave {
  opacity: 0;
  transform: translateY(-10px);
}

.app-message-warning {
  border-color: #ffd666;
  background: #fffbe6;
}

.app-message-error {
  border-color: #ff7875;
  background: #fff2f0;
}

.app-message-success {
  border-color: #95de64;
  background: #f6ffed;
}
