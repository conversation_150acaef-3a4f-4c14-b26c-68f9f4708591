import React, { useEffect, useState } from 'react';
import { createRoot } from 'react-dom/client';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import Diff from './components/Diff';
import type { TraceData } from './components/Diff/types';
import * as styles from './index.module.less';

interface DiffModalProps {
    data: TraceData;
    onClose: () => void;
}

const DiffModal: React.FC<DiffModalProps> = ({ data, onClose }) => {
    const [isClosing, setIsClosing] = useState(false);

    const handleClose = () => {
        setIsClosing(true);
        setTimeout(() => {
            onClose();
        }, 200);
    };

    useEffect(() => {
        const handleEscape = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
                handleClose();
            }
        };

        document.addEventListener('keydown', handleEscape);
        return () => {
            document.removeEventListener('keydown', handleEscape);
        };
    }, []);

    return (
        <ConfigProvider locale={zhCN}>
            <div
                className={`${styles.modalOverlay} ${isClosing ? styles.closing : ''}`}
                onClick={(e) => {
                    if (e.target === e.currentTarget) {
                        handleClose();
                    }
                }}
            >
                <div
                    className={`${styles.modal} ${isClosing ? styles.closing : ''}`}
                    onClick={(e) => {
                        e.stopPropagation();
                    }}
                >
                    <div className={styles.modalHeader}>
                        <h2 className={styles.modalTitle}>润色对比</h2>
                        <button className={styles.closeButton} onClick={handleClose}>
                            ×
                        </button>
                    </div>
                    <div className={styles.modalContent}>
                        <Diff data={data} />
                    </div>
                </div>
            </div>
        </ConfigProvider>
    );
};

// 全局Modal管理器
class DiffModalManager {
    private container: HTMLDivElement | null = null;
    private root: any = null;

    show(data: TraceData) {
        // 如果已经存在Modal，先关闭
        this.hide();

        // 创建容器
        this.container = document.createElement('div');
        this.container.id = 'diff-modal-container';
        document.body.appendChild(this.container);

        // 创建React根并渲染
        this.root = createRoot(this.container);
        this.root.render(
            <DiffModal
                data={data}
                onClose={() => this.hide()}
            />
        );
    }

    hide() {
        if (this.root) {
            this.root.unmount();
            this.root = null;
        }
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
            this.container = null;
        }
    }
}

const diffModalManager = new DiffModalManager();

// 导出显示和隐藏方法
export const showDiffModal = (data: TraceData) => {
    diffModalManager.show(data);
};

export const hideDiffModal = () => {
    diffModalManager.hide();
};

export default DiffModal;

