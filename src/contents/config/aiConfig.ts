/**
 * AI 服务配置
 */

import { AIService } from '../services/aiService';
import { MockAIService } from '../services/mockAIService';

/**
 * AI 配置接口
 */
export interface AIConfig {
  useMock: boolean;
  apiUrl: string;
  apiKey?: string;
  timeout: number;
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: AIConfig = {
  useMock: false, // 禁用 mock，使用真实的 action handler 数据
  apiUrl: 'http://localhost:3000/api',
  timeout: 30000, // 30秒超时
};

/**
 * 获取当前配置
 */
export function getAIConfig(): AIConfig {
  // 可以从 chrome.storage 或环境变量中读取配置
  return {
    ...DEFAULT_CONFIG,
    // 始终禁用 mock，使用 action handler 数据
    useMock: false,
  };
}

/**
 * 获取 AI 服务实例
 */
export function getAIService(): AIService {
  const config = getAIConfig();

  if (config.useMock) {
    console.log('Using Mock AI Service for development');
    return new MockAIService(config.apiUrl, config.apiKey);
  } else {
    console.log('Using Real AI Service');
    return new AIService(config.apiUrl, config.apiKey);
  }
}
