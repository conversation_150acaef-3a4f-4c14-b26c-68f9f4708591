import React from 'react'
import { createRoot } from 'react-dom/client'
import type { Root } from 'react-dom/client'
import { EFloatButtonActionType, EContentsMessageType } from '@src/common/const'
import SelectionBarComponent from '../components/SelectionBar/index'
import AIProcessModalComponent from '../components/AIProcessModal/index'
import { getFloatingButton } from '../components/FloatButton'
import { ScreenShot } from '../components/ScreenShot'
import HiddenChatUI, { type HiddenChatUIRef } from '../components/HiddenChatUI/index'
import { executeXzlParamExtraction } from './xzlBzParamsExtract'

// 导入样式文本用于注入到 Shadow DOM
import selectionBarStyleText from 'data-text:../components/SelectionBar/index.module.less'
import languageSelectorStyleText from 'data-text:../components/LanguageSelector/index.module.less'
import customSelectStyleText from 'data-text:../components/CustomSelect/index.module.less'
import floatButtonStyleText from 'data-text:../components/FloatButton/index.module.less'
import aiProcessModalStyleText from 'data-text:../components/AIProcessModal/index.module.less'
import secondaryActionButtonsStyleText from 'data-text:../components/SecondaryActionButtons/index.module.less'
import hiddenChatUIStyleText from 'data-text:../components/HiddenChatUI/index.module.less'
import screenShotStyleText from 'data-text:../components/ScreenShot/index.module.less'
import loadingStyleText from 'data-text:../components/Loading/index.module.less'
import markdownStyleText from 'data-text:../components/AIProcessModal/markdown.less'
import { handleStartTranslate } from './injectTranslate'

import { configShowFloatingButton } from '../utils/setting'

/**
 * Web Assistant 主管理器
 * 统一管理划词工具栏和浮动按钮
 */
class WebAssistantManager {
  private mainContainer: HTMLDivElement | null = null
  private shadowRoot: ShadowRoot | null = null
  private selectionBarContainer: HTMLDivElement | null = null
  private floatingButtonContainer: HTMLDivElement | null = null
  private screenShotContainer: HTMLDivElement | null = null
  private aiProcessModalContainer: HTMLDivElement | null = null
  private selectionBarRoot: Root | null = null
  private screenShotRoot: Root | null = null
  private floatingButtonRoot: Root | null = null
  private aiProcessModalRoot: Root | null = null


  // 隐藏ChatUI相关属性
  private hiddenChatUIContainer: HTMLDivElement | null = null
  private hiddenChatUIRoot: Root | null = null
  private hiddenChatUIRef: React.RefObject<HiddenChatUIRef> = React.createRef()

  private selectedText: string = ''
  private isSelectionBarVisible: boolean = false
  private isFloatingButtonVisible: boolean = true
  private isAIProcessModalVisible: boolean = false

  // 添加一个标志位，确保 initXzlMutationObserver 只执行一次
  private isXzlMutationObserverInitialized: boolean = false

  // AIProcessModal相关状态
  private currentAIAction: string = ''
  private aiProcessModalDataUpdater: ((data: string, isComplete: boolean, conversationId?: string) => void) | null = null
  private cachedSelectionRect: DOMRect | null = null // 缓存选择区域的位置信息
  private scrollHandler: (() => void) | null = null // 滚动事件处理器
  private isScrollListenerActive: boolean = false // 滚动监听器状态
  private lastMouseEvent: MouseEvent | null = null // 最后一次鼠标事件，用于兜底定位

  constructor() {
    this.init()
  }

  private init(): void {
    try {
      this.createMainContainer()
      this.initEventListeners()
      this.initFloatingButton()
      this.initHiddenChatUI()
    } catch (error) {
      console.error('WebAssistantManager: Initialization failed:', error)
    }
  }

  /**
   * 创建主容器和 Shadow DOM
   */
  private createMainContainer(): void {

    // 检查是否已经存在主容器，避免重复创建
    const existingContainer = document.getElementById(
      'web-assistant-main-container'
    )
    if (existingContainer) {
      return
    }

    // 创建主容器
    this.mainContainer = document.createElement('div')
    this.mainContainer.id = 'web-assistant-main-container'
    this.mainContainer.className = 'web-assistant-container'

    // 创建 Shadow Root
    this.shadowRoot = this.mainContainer.attachShadow({ mode: 'open' })

    // 添加样式
    const style = document.createElement('style')
    const styleTextList = [selectionBarStyleText, languageSelectorStyleText, customSelectStyleText, floatButtonStyleText, aiProcessModalStyleText, secondaryActionButtonsStyleText, hiddenChatUIStyleText, screenShotStyleText, loadingStyleText, markdownStyleText]
    style.textContent = this.getStyles() + '\n' + styleTextList.join('\n')

    this.shadowRoot.appendChild(style)

    // 创建划词工具栏容器
    this.selectionBarContainer = document.createElement('div')
    this.selectionBarContainer.id = 'web-assistant-selection-bar'
    this.selectionBarContainer.className =
      'web-assistant-selection-bar-container'
    this.shadowRoot.appendChild(this.selectionBarContainer)

    // 创建浮动按钮容器
    this.floatingButtonContainer = document.createElement('div')
    this.floatingButtonContainer.id = 'web-assistant-floating-button'
    this.floatingButtonContainer.className =
      'web-assistant-floating-button-container'
    this.shadowRoot.appendChild(this.floatingButtonContainer)

    // 创建截屏容器
    this.screenShotContainer = document.createElement('div')
    this.screenShotContainer.id = 'screen-shot'
    this.screenShotContainer.className =
      'screen-shot-container'
    this.shadowRoot.appendChild(this.screenShotContainer)


    // 创建AIProcessModal容器
    this.aiProcessModalContainer = document.createElement('div')
    this.aiProcessModalContainer.id = 'web-assistant-ai-process-modal'
    this.aiProcessModalContainer.className =
      'web-assistant-ai-process-modal-container'

    // 确保容器有正确的初始样式
    this.aiProcessModalContainer.style.position = 'absolute'
    this.aiProcessModalContainer.style.zIndex = '10001'
    this.aiProcessModalContainer.style.pointerEvents = 'auto'
    this.aiProcessModalContainer.style.display = 'block'

    this.shadowRoot.appendChild(this.aiProcessModalContainer)

    // 创建隐藏ChatUI容器
    this.hiddenChatUIContainer = document.createElement('div')
    this.hiddenChatUIContainer.id = 'web-assistant-hidden-chatui'
    this.hiddenChatUIContainer.className = 'web-assistant-hidden-chatui-container'
    this.shadowRoot.appendChild(this.hiddenChatUIContainer)

    // 添加到页面
    document.body.appendChild(this.mainContainer)
  }

  /**
   * 初始化事件监听器
   */
  private initEventListeners(): void {
    // 监听文本选择
    document.addEventListener('mouseup', this.handleMouseUp)
    document.addEventListener('keyup', this.handleKeyUp)

    // 监听点击事件（用于隐藏工具栏）
    document.addEventListener('click', this.handleDocumentClick)

    // 监听选择变化
    // document.addEventListener('selectionchange', this.handleSelectionChange)

    // 监听页面卸载，清理所有弹窗
    window.addEventListener('beforeunload', this.hideAllModals)

    // 监听页面可见性事件，清理所有弹窗
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // this.hideAllModals()
      } else {
        this.handleFloatingButton()
      }
    })

    // 初始化滚动事件处理器
    this.scrollHandler = this.handleScroll.bind(this)
    // 添加特定域名下的MutationObserver监听器
    // if (window.location.origin === 'https://eiplite.htsc.com.cn') {
    //   this.initXzlMutationObserver();
    // }
  }

  /**
   * 初始化新涨乐参数提取的MutationObserver
   */
  private initXzlMutationObserver(): void {
    // 检查是否已经初始化过了
    if (this.isXzlMutationObserverInitialized) {
      return;
    }

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as HTMLElement;
              // 检查是否有包含qz-orchestration-drawer类的元素
              // const drawerElements = element.classList?.contains('qz-orchestration-drawer')
              //   ? [element]
              //   : element.querySelectorAll?.('.qz-orchestration-drawer') || [];

              // if (!drawerElements.length) return;

              // // 检查子元素中是否包含文本为"新涨乐开发工作流"的元素
              // // 根据新的DOM结构，需要查找包含该文本的span元素
              // console.log('drawerElements', drawerElements);
              // console.log('drawerElements[0]', drawerElements[0]);

              const targetSpans = document.querySelectorAll('span')
              console.log('targetSpans', targetSpans);

              const hasXZLText = Array.from(targetSpans).some(span => {
                console.log('span.textContent', span.textContent);

                return span.textContent.trim() === '新涨乐开发工作流';
              }
              );
              console.log('hasXZLText', hasXZLText);


              if (hasXZLText) {
                // 触发参数提取处理
                this.handleXzlParamExtract();

                // 设置标志位，表示已经初始化过了
                this.isXzlMutationObserverInitialized = true;
              }
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

  }


  /**
   * 处理滚动事件
   */
  private handleScroll = (e: Event): void => {
    // 如果有可见的划词工具栏或AI处理弹窗，重新定位它们
    if (this.isSelectionBarVisible && this.selectionBarContainer) {
      this.positionSelectionBar()
    }

    if (this.isAIProcessModalVisible && this.aiProcessModalContainer) {
      this.positionAIProcessModal()
    }
  }

  /**
   * 启用滚动监听器
   */
  private enableScrollListener(): void {

    if (!this.isScrollListenerActive && this.scrollHandler) {
      // 监听window和document的滚动事件
      window.addEventListener('scroll', this.scrollHandler, { passive: true })
      document.addEventListener('scroll', this.scrollHandler, { passive: true })

      // 监听所有可能的滚动容器
      this.addScrollListenerToAllScrollableElements()

      this.isScrollListenerActive = true
    }
  }

  /**
   * 禁用滚动监听器
   */
  private disableScrollListener(): void {
    if (this.isScrollListenerActive && this.scrollHandler) {
      window.removeEventListener('scroll', this.scrollHandler)
      document.removeEventListener('scroll', this.scrollHandler)

      // 移除所有滚动容器的监听器
      this.removeScrollListenerFromAllScrollableElements()

      this.isScrollListenerActive = false
    }
  }

  /**
   * 为所有可滚动元素添加滚动监听器
   */
  private addScrollListenerToAllScrollableElements(): void {
    if (!this.scrollHandler) return

    // 查找所有可能的滚动容器
    const scrollableElements = this.findScrollableElements()

    scrollableElements.forEach(element => {
      element.addEventListener('scroll', this.scrollHandler!, { passive: true })
    })
  }

  /**
   * 从所有可滚动元素移除滚动监听器
   */
  private removeScrollListenerFromAllScrollableElements(): void {
    if (!this.scrollHandler) return

    // 查找所有可能的滚动容器
    const scrollableElements = this.findScrollableElements()

    scrollableElements.forEach(element => {
      element.removeEventListener('scroll', this.scrollHandler!)
    })
  }

  /**
   * 查找页面中所有可滚动的元素
   */
  private findScrollableElements(): Element[] {
    const scrollableElements: Element[] = []

    // 获取所有元素
    const allElements = document.querySelectorAll('*')

    allElements.forEach(element => {
      const computedStyle = window.getComputedStyle(element)
      const overflowX = computedStyle.overflowX
      const overflowY = computedStyle.overflowY

      // 检查是否有滚动能力
      if (
        (overflowY === 'scroll' || overflowY === 'auto' || overflowX === 'scroll' || overflowX === 'auto') &&
        (element.scrollHeight > element.clientHeight || element.scrollWidth > element.clientWidth)
      ) {
        scrollableElements.push(element)
      }
    })

    return scrollableElements
  }

  /**
   * 处理鼠标抬起事件
   */
  private handleMouseUp = (event: MouseEvent): void => {
    console.log('Mouse up event triggered')

    // 缓存鼠标事件，用于兜底定位
    this.lastMouseEvent = event

    // 延迟处理，确保选择已完成
    setTimeout(() => {
      this.checkSelection(event)
    }, 10)
  }

  /**
   * 处理键盘事件
   */
  private handleKeyUp = (event: KeyboardEvent): void => {
    // 延迟处理，确保选择已完成
    setTimeout(() => {
      this.checkSelection()
    }, 10)
  }

  /**
   * 处理选择变化事件
   */
  private handleSelectionChange = (): void => {
    // 延迟处理，避免在选择过程中过早隐藏工具栏
    setTimeout(() => {
      const selection = window.getSelection()

      if (!selection || selection.toString().trim() === '') {
        console.log('Selection cleared, hiding toolbar')
        this.hideSelectionBar()
      }
    }, 100)
  }

  /**
   * 处理文档点击事件
   */
  private handleDocumentClick = (event: MouseEvent): void => {
    if ((this.isSelectionBarVisible && this.selectionBarContainer) || (this.isAIProcessModalVisible && this.aiProcessModalContainer)) {
      const eventPath = event.composedPath()

      const isInsideMainContainer =
        this.mainContainer && eventPath.includes(this.mainContainer)
      const isInsideShadowDOM =
        this.shadowRoot &&
        eventPath.some((node) => {
          // 确保 node 是 Node 类型再调用 contains
          return node instanceof Node && this.shadowRoot!.contains(node)
        })
      const isInsideSelectionBar =
        this.selectionBarContainer &&
        eventPath.some((node) => {
          // 确保 node 是 Node 类型再调用 contains
          return node instanceof Node && this.selectionBarContainer!.contains(node)
        })

      const isInsideAiProcessModal =
        this.aiProcessModalContainer &&
        eventPath.some((node) => {
          // 确保 node 是 Node 类型再调用 contains
          return node instanceof Node && this.aiProcessModalContainer!.contains(node)
        })

      // 如果点击在我们的组件内部，不隐藏工具栏
      if (isInsideShadowDOM || isInsideMainContainer || isInsideSelectionBar || isInsideAiProcessModal) {
        console.log('Click inside selection bar area, not hiding')
        return
      }

      // 检查是否还有选中的文本
      const selection = window.getSelection()
      if (!selection || selection.toString().trim() === '') {
        console.log('Hiding selection bar due to outside click')
        this.hideSelectionBar()
        this.hideAIProcessModal()
      }
    }
  }

  /**
   * 检查当前选择
   */
  private checkSelection(event?: MouseEvent): void {
    const selection = window.getSelection()
    if (!selection) return

    const newSelectedText = selection.toString().trim()

    if (newSelectedText && newSelectedText !== this.selectedText) {
      //这里顺序不能改，涉及到时序问题
      this.hideAIProcessModal()
      this.selectedText = newSelectedText
      this.showSelectionBar(event)
    } else if (!newSelectedText && this.isSelectionBarVisible) {
      // 简化逻辑：选择清空时直接隐藏SelectionBar
      // AIProcessModal有自己独立的生命周期管理
      this.hideSelectionBar()
    }
  }

  /**
   * 显示划词工具栏
   */
  private showSelectionBar(event?: MouseEvent): void {
    if (!this.selectedText) return

    console.log(
      'WebAssistantManager: Showing selection bar for text:',
      this.selectedText
    )

    // 预先计算并缓存选择区域的位置信息，避免后续操作时选择状态丢失
    this.cacheSelectionPosition()

    // 如果容器不存在或已被移除，重新创建
    if (!this.selectionBarContainer || !this.selectionBarContainer.parentNode) {
      this.createSelectionBarContainer()
    }

    // 创建 React root（如果还没有）
    if (!this.selectionBarRoot) {
      this.selectionBarRoot = createRoot(this.selectionBarContainer)
    }

    // 渲染组件
    this.selectionBarRoot.render(
      React.createElement(SelectionBarComponent, {
        selectedText: this.selectedText,
        onAction: this.handleSelectionBarAction,
        onClose: this.hideSelectionBar,
      })
    )

    // 定位工具栏
    this.positionSelectionBar()

    // 显示工具栏（保持原有的动画效果）
    this.selectionBarContainer.classList.add('show')
    this.isSelectionBarVisible = true

    // 启用滚动监听器
    this.enableScrollListener()
  }

  /**
   * 初始化截屏工具
   */
  private async createScreenShot(): Promise<void> {
    if (!this.screenShotContainer) return

    this.screenShotRoot = createRoot(this.screenShotContainer)

    // 渲染组件
    this.screenShotRoot.render(React.createElement(ScreenShot, {
      removeScreenShot: this.removeScreenShot.bind(this),
    }))

  }

  /**
   * 清理截屏工具
   */
  private async removeScreenShot() {
    if (this.screenShotRoot) {
      try {
        this.screenShotRoot.unmount()
        this.screenShotRoot = null
      } catch (error) {
        console.error('WebAssistantManager: Failed to cleanup ScreenShot:', error)
      }
    }
  }

  /**
   * 创建悬浮球
   * @returns 
   */
  private createFloatingButton() {
    if (!this.floatingButtonContainer) return

    this.floatingButtonRoot = createRoot(this.floatingButtonContainer)
    const FloatingButton = getFloatingButton({
      onAction: this.handleFloatingButtonAction,
    })

    // 渲染组件
    this.floatingButtonRoot.render(FloatingButton)
  }

  /**
   * 初始化悬浮按钮
   */
  private async initFloatingButton(): Promise<void> {
    const showFloatBtn = await configShowFloatingButton()
    if (!showFloatBtn) {
      return
    }
    if (!this.floatingButtonRoot) {
      this.createFloatingButton()
    }
  }

  /**
 * 清理悬浮按钮实例
 */
  private async handleFloatingButton() {
    const showFloatBtn = await configShowFloatingButton()
    if (showFloatBtn) {
      if (!this.floatingButtonRoot) {   // 如果没有悬浮按钮，则创建悬浮按钮
        this.createFloatingButton()
      }
      return
    }
    if (this.floatingButtonRoot) {
      try {
        this.floatingButtonRoot.unmount()
        this.floatingButtonRoot = null
      } catch (error) {
        console.error('WebAssistantManager: Failed to cleanup FloatingButton:', error)
      }
    }
  }

  /**
   * 初始化隐藏的ChatUI实例
   */
  private initHiddenChatUI(): void {
    if (!this.hiddenChatUIContainer) {
      console.error('WebAssistantManager: Hidden ChatUI container not found')
      return
    }

    try {
      // 创建 React root
      this.hiddenChatUIRoot = createRoot(this.hiddenChatUIContainer)

      // 渲染隐藏的ChatUI组件
      this.hiddenChatUIRoot.render(
        React.createElement(HiddenChatUI, {
          ref: this.hiddenChatUIRef,
          selectedText: this.selectedText,
        })
      )
    } catch (error) {
      console.error('WebAssistantManager: Failed to initialize hidden ChatUI:', error)
    }
  }

  /**
   * 获取隐藏ChatUI的引用，用于调用onSend方法
   */
  public getHiddenChatUIRef(): HiddenChatUIRef | null {
    return this.hiddenChatUIRef.current
  }

  /**
   * 直接调用隐藏ChatUI的onSend方法
   */
  public async callHiddenChatUIOnsend(type: string, content: string, options?: any, attachments?: any[]): Promise<any> {
    const chatUIRef = this.getHiddenChatUIRef()

    if (!chatUIRef) {
      throw new Error('Hidden ChatUI not initialized')
    }

    try {
      console.log('callHiddenChatUIOnsend 传给sck onsend的参数', options);

      const result = await chatUIRef.chatContext.onSend(type, content, options, attachments)
      return result
    } catch (error) {
      console.error('chatUIRef.chatContext.onSend 请求出错', error);
      throw error
    }
  }

  /**
   * 隐藏划词工具栏
   */
  private hideSelectionBar = (): void => {
    if (this.selectionBarContainer && this.isSelectionBarVisible) {
      // 添加隐藏动画类
      this.selectionBarContainer.classList.remove('show')
      this.selectionBarContainer.classList.add('hide')

      // 等待动画完成后移除DOM元素
      setTimeout(() => {
        if (this.selectionBarContainer && this.selectionBarContainer.parentNode) {
          this.selectionBarContainer.remove()
        }

        // 清理React root
        if (this.selectionBarRoot) {
          try {
            this.selectionBarRoot.unmount()
          } catch (error) {
            console.warn('Error unmounting selection bar:', error)
          }
          this.selectionBarRoot = null
        }
      }, 200) // 与CSS动画时长匹配

      this.isSelectionBarVisible = false
      // 保留selectedText和cachedSelectionRect给AIProcessModal使用

      // 如果AI处理弹窗也不可见，则禁用滚动监听器
      if (!this.isAIProcessModalVisible) {
        this.disableScrollListener()
      }
    }
  }

  /**
   * 创建划词工具栏容器
   */
  private createSelectionBarContainer(): void {
    // 创建划词工具栏容器
    this.selectionBarContainer = document.createElement('div')
    this.selectionBarContainer.id = 'web-assistant-selection-bar'
    this.selectionBarContainer.className = 'web-assistant-selection-bar-container'
    this.shadowRoot.appendChild(this.selectionBarContainer)
  }

  /**
   * 显示AIProcessModal
   */
  private showAIProcessModal(action: string): void {
    if (!this.selectedText) return

    // 确保有缓存的选择位置信息
    if (!this.cachedSelectionRect) {
      this.cacheSelectionPosition()
    }

    // 设置当前AI操作和状态
    this.currentAIAction = action
    this.isAIProcessModalVisible = true

    // 先隐藏SelectionBar（实现互斥显示）
    this.hideSelectionBar()

    // 如果容器不存在或已被移除，重新创建
    if (!this.aiProcessModalContainer || !this.aiProcessModalContainer.parentNode) {
      this.createAIProcessModalContainer()
    }

    // 创建React root（如果还没有）
    if (!this.aiProcessModalRoot) {
      this.aiProcessModalRoot = createRoot(this.aiProcessModalContainer)
    }

    // 渲染AIProcessModal组件（不传递position，由容器定位控制）
    this.aiProcessModalRoot.render(
      React.createElement(AIProcessModalComponent, {
        isVisible: true,
        selectedText: this.selectedText,
        actionType: this.currentAIAction,
        onClose: this.hideAIProcessModal,
        onRegisterDataUpdater: this.handleAIProcessModalDataUpdate,
        onRetriggerTranslation: this.handleRetriggerTranslation,
      })
    )

    // 定位弹窗（使用与SelectionBar相同的方式）
    this.positionAIProcessModal()

    // 启用滚动监听器
    this.enableScrollListener()
  }

  /**
   * 处理 AIProcessModal 数据更新
   */
  private handleAIProcessModalDataUpdate = (updater: (data: string, isComplete: boolean, conversationId?: string) => void): void => {
    this.aiProcessModalDataUpdater = updater;
  }

  /**
   * 更新 AIProcessModal 数据（供 action handler 调用）
   */
  public updateAIProcessModalData = (data: string, isComplete: boolean, conversationId?: string): void => {
    if (this.aiProcessModalDataUpdater) {
      this.aiProcessModalDataUpdater(data, isComplete, conversationId);
    }
  }

  /**
   * 处理重新触发翻译
   */
  private handleRetriggerTranslation = (languageOptions: any): void => {
    console.log('handleRetriggerTranslation:', languageOptions);

    // 验证语言选项参数
    if (!languageOptions || !languageOptions.srcLang || !languageOptions.tgtLang) {
      console.error('handleRetriggerTranslation: Invalid language options', languageOptions);
      return;
    }

    // 验证选中的文本
    if (!this.selectedText) {
      console.error('handleRetriggerTranslation: No selected text available');
      return;
    }

    // 重置AIProcessModal的状态，准备显示新的翻译结果
    if (this.aiProcessModalDataUpdater) {
      this.aiProcessModalDataUpdater('', false); // 清空内容，重新开始处理
    }

    // 重新调用翻译API，传递新的语言选项
    this.callHiddenChatUIOnsend('text', `${this.selectedText}`, {
      agentId: 'translate',
      conversationId: "",
      hideConversation: true,
      extendParams: {
        msgChannel: 'contentScript',
        translateOptions: {
          src_lang: languageOptions.srcLang,
          tgt_lang: languageOptions.tgtLang
        }
      }
    })
  }

  /**
   * 隐藏AIProcessModal
   */
  private hideAIProcessModal = (): void => {
    if (this.aiProcessModalContainer && this.isAIProcessModalVisible) {
      // 直接移除DOM元素
      this.aiProcessModalContainer.remove()

      // 清理React root
      if (this.aiProcessModalRoot) {
        try {
          this.aiProcessModalRoot.unmount()
        } catch (error) {
          console.warn('Error unmounting AI process modal:', error)
        }
        this.aiProcessModalRoot = null
      }

      // 重置状态
      this.isAIProcessModalVisible = false
      this.currentAIAction = ''
      this.selectedText = ''
      this.cachedSelectionRect = null // 清理缓存的选择位置

      // 如果划词工具栏也不可见，则禁用滚动监听器
      if (!this.isSelectionBarVisible) {
        this.disableScrollListener()
      }
    }
  }

  /**
   * 创建AI处理弹窗容器
   */
  private createAIProcessModalContainer(): void {
    // 创建AIProcessModal容器
    this.aiProcessModalContainer = document.createElement('div')
    this.aiProcessModalContainer.id = 'web-assistant-ai-process-modal'
    this.aiProcessModalContainer.className = 'web-assistant-ai-process-modal-container'

    // 确保容器有正确的初始样式
    this.aiProcessModalContainer.style.position = 'absolute'
    this.aiProcessModalContainer.style.zIndex = '10001'
    this.aiProcessModalContainer.style.pointerEvents = 'auto'
    this.aiProcessModalContainer.style.display = 'block'

    this.shadowRoot.appendChild(this.aiProcessModalContainer)
  }

  /**
   * 缓存当前选择区域的位置信息
   */
  private cacheSelectionPosition(): void {
    const rect = this.getAccurateSelectionRect()
    this.cachedSelectionRect = rect
    console.log('position cachedSelectionRect', this.cachedSelectionRect);
  }

  /**
   * 定位AI处理弹窗（使用与SelectionBar相同的方式）
   */
  private positionAIProcessModal(): void {
    if (!this.aiProcessModalContainer) return

    // 确保容器是可见的
    this.aiProcessModalContainer.style.display = 'block'

    // 尝试获取当前精确的选择位置，如果没有则使用缓存的位置
    let rect: DOMRect | null = this.getAccurateSelectionRect()

    if (!rect && this.cachedSelectionRect) {
      // 如果没有当前选择，使用缓存的位置
      rect = this.cachedSelectionRect
      console.log('positionAIProcessModal: using cachedSelectionRect:', rect);
    }

    if (!rect) {
      console.warn(
        'WebAssistantManager: No selection found and no cached position, using default position'
      )
      // 使用默认位置（相对于文档的绝对定位）
      const scrollX = window.pageXOffset || document.documentElement.scrollLeft
      const scrollY = window.pageYOffset || document.documentElement.scrollTop
      this.aiProcessModalContainer.style.left = `${50 + scrollX}px`
      this.aiProcessModalContainer.style.top = `${50 + scrollY}px`
      return
    }

    // 使用与SelectionBar完全相同的定位逻辑
    const offsetY = 8
    const modalWidth = 500 // AI弹窗的实际宽度
    const modalHeight = 400 // AI弹窗的估算高度

    // 获取页面滚动偏移量
    const scrollX = window.pageXOffset || document.documentElement.scrollLeft
    const scrollY = window.pageYOffset || document.documentElement.scrollTop

    // 将视口坐标转换为文档坐标（相对于文档流的绝对定位）
    let left = rect.left + scrollX
    let top = rect.bottom + scrollY + offsetY

    // 获取文档尺寸和视口尺寸
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    const documentWidth = Math.max(document.documentElement.scrollWidth, document.body.scrollWidth)
    const documentHeight = Math.max(document.documentElement.scrollHeight, document.body.scrollHeight)

    // 确保弹窗不会超出视口右边界（考虑当前滚动位置）
    if (left - scrollX + modalWidth > viewportWidth) {
      // 如果超出右边界，调整到选中文本的右边界对齐
      left = rect.right + scrollX - modalWidth
    }

    // 确保弹窗不会超出视口下边界（考虑当前滚动位置）
    if (top - scrollY + modalHeight > viewportHeight) {
      // 如果超出下边界，显示在选中文本的上方
      top = rect.top + scrollY - modalHeight - offsetY
    }

    // 确保不会超出文档边界
    left = Math.max(5, Math.min(left, documentWidth - modalWidth - 5))
    top = Math.max(5, Math.min(top, documentHeight - modalHeight - 5))

    // 直接设置容器位置（与SelectionBar相同的方式）
    console.log('positionAIProcessModal: setting position:', { left, top, scrollX, scrollY, viewportWidth, viewportHeight });
    this.aiProcessModalContainer.style.left = `${left}px`
    this.aiProcessModalContainer.style.top = `${top}px`
  }

  /**
   * 隐藏所有弹窗（用于清理状态）
   */
  private hideAllModals = (): void => {
    this.hideSelectionBar()
    this.hideAIProcessModal()
    this.cleanupHiddenChatUI()
    // 确保禁用滚动监听器
    this.disableScrollListener()
  }

  /**
   * 清理隐藏的ChatUI实例
   */
  private cleanupHiddenChatUI(): void {
    if (this.hiddenChatUIRoot) {
      try {
        this.hiddenChatUIRoot.unmount()
        this.hiddenChatUIRoot = null
      } catch (error) {
        console.error('WebAssistantManager: Failed to cleanup hidden ChatUI:', error)
      }
    }
  }

  /**
   * 设置AIProcessModal的显示状态（保留用于兼容性）
   */
  public setAIProcessModalVisible(visible: boolean): void {
    this.isAIProcessModalVisible = visible
  }

  /**
   * 获取更精确的选择位置信息
   */
  private getAccurateSelectionRect(): DOMRect | null {
    const selection = window.getSelection()
    if (!selection || selection.rangeCount === 0) return null

    const range = selection.getRangeAt(0)

    // 方法1: 使用 Range.getBoundingClientRect() (最常用)
    let rect = range.getBoundingClientRect()

    // 方法2: 如果rect无效，尝试使用选择的实际内容节点
    if (rect.width === 0 && rect.height === 0) {
      const rects = range.getClientRects()
      if (rects.length > 0) {
        // 使用第一个有效的rect，或者合并所有rect
        rect = rects[0]

        // 如果有多个rect（跨行选择），计算合并后的边界
        if (rects.length > 1) {
          let minLeft = rects[0].left
          let minTop = rects[0].top
          let maxRight = rects[0].right
          let maxBottom = rects[0].bottom

          for (let i = 1; i < rects.length; i++) {
            minLeft = Math.min(minLeft, rects[i].left)
            minTop = Math.min(minTop, rects[i].top)
            maxRight = Math.max(maxRight, rects[i].right)
            maxBottom = Math.max(maxBottom, rects[i].bottom)
          }

          // 创建合并后的rect
          rect = new DOMRect(minLeft, minTop, maxRight - minLeft, maxBottom - minTop)
        }
      }
    }

    // 方法3: 如果仍然无效，尝试使用选择节点的边界
    if (rect.width === 0 && rect.height === 0) {
      const startContainer = range.startContainer
      const endContainer = range.endContainer

      // 如果是文本节点，获取其父元素
      const startElement = startContainer.nodeType === Node.TEXT_NODE
        ? startContainer.parentElement
        : startContainer as Element
      const endElement = endContainer.nodeType === Node.TEXT_NODE
        ? endContainer.parentElement
        : endContainer as Element

      if (startElement && startElement.getBoundingClientRect) {
        const startRect = startElement.getBoundingClientRect()
        const endRect = endElement && endElement.getBoundingClientRect
          ? endElement.getBoundingClientRect()
          : startRect

        // 合并开始和结束元素的边界
        const left = Math.min(startRect.left, endRect.left)
        const top = Math.min(startRect.top, endRect.top)
        const right = Math.max(startRect.right, endRect.right)
        const bottom = Math.max(startRect.bottom, endRect.bottom)

        rect = new DOMRect(left, top, right - left, bottom - top)
      }
    }

    // 方法4: 最后的兜底方案 - 使用鼠标位置
    if (rect.width === 0 && rect.height === 0 && this.lastMouseEvent) {
      const mouseX = this.lastMouseEvent.clientX
      const mouseY = this.lastMouseEvent.clientY
      rect = new DOMRect(mouseX - 1, mouseY - 1, 2, 2)
    }

    console.log('getAccurateSelectionRect result:', {
      method: rect.width > 0 ? 'range/rects/element' : 'fallback',
      rect,
      selection: selection.toString().substring(0, 50) + '...'
    })

    return rect.width > 0 || rect.height > 0 ? rect : null
  }

  /**
   * 定位划词工具栏
   */
  private positionSelectionBar(): void {
    if (!this.selectionBarContainer) return

    const rect = this.getAccurateSelectionRect()
    if (!rect) {
      console.warn('Unable to get selection rect for positioning')
      return
    }

    console.log('position start------------', { rect });

    // 计算悬浮球的位置，让它出现在选中文本的下方
    const offsetY = 8 // 垂直偏移，避免紧贴文本
    const barWidth = 300 // 估算的悬浮球宽度
    const barHeight = 50 // 估算的悬浮球高度

    // 获取页面滚动偏移量
    const scrollX = window.pageXOffset || document.documentElement.scrollLeft
    const scrollY = window.pageYOffset || document.documentElement.scrollTop

    // 将视口坐标转换为文档坐标（相对于文档流的绝对定位）
    let left = rect.left + scrollX
    let top = rect.bottom + scrollY + offsetY

    // 获取文档尺寸和视口尺寸
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    const documentWidth = Math.max(document.documentElement.scrollWidth, document.body.scrollWidth)
    const documentHeight = Math.max(document.documentElement.scrollHeight, document.body.scrollHeight)

    // 确保悬浮球不会超出视口右边界（考虑当前滚动位置）
    if (left - scrollX + barWidth > viewportWidth) {
      // 如果超出右边界，调整到选中文本的右边界对齐
      left = rect.right + scrollX - barWidth
    }

    // 确保悬浮球不会超出视口下边界（考虑当前滚动位置）
    if (top - scrollY + barHeight > viewportHeight) {
      // 如果超出下边界，显示在选中文本的上方
      top = rect.top + scrollY - barHeight - offsetY
    }

    // 确保不会超出文档边界
    left = Math.max(5, Math.min(left, documentWidth - barWidth - 5))
    top = Math.max(5, Math.min(top, documentHeight - barHeight - 5))

    console.log('position ', { left, top, scrollX, scrollY, viewportWidth, viewportHeight });

    this.selectionBarContainer.style.left = `${left}px`
    this.selectionBarContainer.style.top = `${top}px`
  }

  /**
   * 处理划词工具栏动作
   */
  private handleSelectionBarAction = (action: string): void => {
    console.log('handleSelectionBarAction:', action);

    // 导入TextOperations和isAIAction函数
    const { textOperations } = require('../utils/textOperations');
    const { isAIAction } = require('../../config/menuItems')

    if (isAIAction(action) || action.includes('custom-skill-')) {
      // 在AI操作开始前，立即缓存当前选择状态，防止后续UI交互导致选择丢失
      textOperations.cacheCurrentSelection();

      // AI操作：显示AIProcessModal，隐藏SelectionBar
      this.showAIProcessModal(action)

      // 准备extendParams参数
      const extendParams: any = {
        msgChannel: 'contentScript'
      };

      // 只有当action为'xzl-param-tag'时才添加skillId参数
      if (action === 'xzl-param-tag') {
        // 根据环境设置不同的skillId，测试环境使用52，生产环境使用25
        const isProd = process.env.PLASMO_TAG === 'prod';
        extendParams.skillId = isProd ? 25 : 52;
      }
      if (action.includes('custom-skill-')) {
        extendParams.skillId = action.split('-')[2];
      }

      //调起hideChatui的onSend
      this.callHiddenChatUIOnsend('text', `${this.selectedText}`, {
        agentId: action === 'xzl-param-tag' || action.includes('custom-skill-') ? 'customize' : action,
        conversationId: "",
        hideConversation: true,
        extendParams
      })
    } else {
      // 非AI操作：保持原有逻辑
      switch (action) {
        case 'open-panel':
          this.handleOpenPanel()
          break
        default:
          console.log('WebAssistantManager: Unknown action:', action)
      }
    }
  }

  /**
   * 处理浮动按钮动作
   */
  private handleFloatingButtonAction = (
    action: EFloatButtonActionType
  ): void => {
    switch (action) {
      case EFloatButtonActionType.Summary:
        this.handleSummary()
        break
      case EFloatButtonActionType.Translate:
        this.handleTranslate()
        break
      case EFloatButtonActionType.Screenshot:
        this.handleScreenshot()
        break
      case EFloatButtonActionType.OpenPanel:
        this.handleOpenPanel()
        break
      case EFloatButtonActionType.XzlParamExtract:
        this.handleXzlParamExtract()
        break
      default:
        console.log(
          'WebAssistantManager: Unknown floating button action:',
          action
        )
    }
  }

  private handleFloatingButtonSendMessage(data: any): void {
    chrome.runtime.sendMessage({
      type: EContentsMessageType.FloatingButton,
      data,
    })
  }

  /**
   * 处理打开面板
   */
  private handleOpenPanel(): void {
    this.handleFloatingButtonSendMessage({
      action: EFloatButtonActionType.OpenPanel,
    })
  }

  /**
   * 处理截图功能
   */
  private handleScreenshot(): void {
    // this.handleFloatingButtonSendMessage({
    //   action: EFloatButtonActionType.Screenshot,
    // })
    // 调起区域截图
    try {
      this.createScreenShot()
    } catch (e) {
      console.error('启动区域截图失败:', e)
    }
  }

  private async handleSummary(): Promise<void> {
    this.handleFloatingButtonSendMessage({
      action: EFloatButtonActionType.Summary,
    })
  }

  private async handleTranslate(): Promise<void> {
    try {
      handleStartTranslate()

    } catch (error) {
      console.error('WebAssistantManager: Translate failed:', error)
      // 降级到原有的事件传递机制
      this.handleFloatingButtonSendMessage({
        action: EFloatButtonActionType.Translate,
      })
    }
  }

  /**
   * 处理新涨乐标注参数提取功能
   */
  private async handleXzlParamExtract(): Promise<void> {
    try {
      console.log('WebAssistantManager: 开始执行新涨乐标注参数提取...')

      // 执行参数提取（包含完整的6个步骤）
      const result = await executeXzlParamExtraction()

      if (result.success && result.data) {
        // 提取成功，显示结果
        console.log('WebAssistantManager: 参数提取成功:', result.data)

        // 构建详细的结果信息
        const extractedFieldsCount = Object.keys(result.data.extractedFields).length;
        const extractedFieldsPreview = Object.entries(result.data.extractedFields)
          .slice(0, 3) // 只显示前3个字段作为预览
          .map(([key, value]) => `${key}: ${JSON.stringify(value)}`)
          .join('\n');

        // 将完整结果复制到剪贴板
        try {
          const resultText = JSON.stringify({
            基础参数: {
              traceId: result.data.traceId,
              workflowId: result.data.workflowId,
              workflowInstanceId: result.data.workflowInstanceId,
              timestamp: result.data.timestamp,
              时间: new Date(result.data.timestamp).toLocaleString()
            },
            提取字段: result.data.extractedFields
          }, null, 2);

          // await navigator.clipboard.writeText(resultText);
        } catch (clipboardError) {

          // 降级：只复制基础参数
          try {
            const basicResultText = `traceId: ${result.data.traceId}\nworkflowId: ${result.data.workflowId}\nworkflowInstanceId: ${result.data.workflowInstanceId}`;
            // await navigator.clipboard.writeText(basicResultText);
            // console.log('WebAssistantManager: 基础参数已复制到剪贴板');
          } catch (basicClipboardError) {
            console.warn('WebAssistantManager: 基础参数复制也失败:', basicClipboardError);
          }
        }

      } else {
        // 提取失败，显示错误信息
        console.error('WebAssistantManager: 参数提取失败:', result.error)
        alert(`新涨乐标注参数提取失败：

错误信息: ${result.error}

请检查：
1. 页面是否包含正确的 traceId 数据
2. localStorage 中是否存在 xzl_biaozhu_config 配置
3. 网络连接是否正常
4. 控制台是否有更详细的错误信息`)
      }

    } catch (error) {
      console.error('WebAssistantManager: 新涨乐标注参数提取异常:', error)

    }
  }

  /**
   * 获取样式
   */
  private getStyles(): string {
    return `
      /* Web Assistant 主样式文件 */

      /* 重置样式 */
      * {
        box-sizing: border-box;
      }

      /* 主容器样式 */
      .web-assistant-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 0;
        height: 0;
        z-index: 2147483647;
        pointer-events: none;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        font-size: 14px;
        line-height: 1.4;
      }

      /* 划词工具栏容器 */
      .web-assistant-selection-bar-container {
        position: absolute;
        opacity: 0;
        visibility: hidden;
        pointer-events: auto;
        transform: translateY(-10px) scale(0.95);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .web-assistant-selection-bar-container.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0) scale(1);
        z-index: 100001;
      }

      .web-assistant-selection-bar-container.hide {
        opacity: 0;
        visibility: hidden;
        transform: translateY(-5px) scale(0.98);
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      }

      /* 浮动按钮容器、截屏容器 */
      .web-assistant-floating-button-container, .screen-shot-container {
        position: fixed;
        pointer-events: auto;
        z-index: 2147483647;
      }

      /* AIProcessModal容器 */
      .web-assistant-ai-process-modal-container {
        position: absolute;
        pointer-events: auto;
        z-index: 10001; /* 比SelectionBar更高的层级 */
        font-size: 14px;
      }

      /* 确保所有子元素都有正确的指针事件 */
      #web-assistant-selection-bar,
      #web-assistant-floating-button,
      #web-assistant-ai-process-modal {
        pointer-events: auto;
      }
    `
  }
}

// 防止重复初始化
declare global {
  interface Window {
    webAssistantManagerInitialized?: boolean
    webAssistantManager?: any  // 使用 any 避免循环依赖问题
  }
}

// 初始化 Web Assistant Manager
if (!window.webAssistantManagerInitialized) {
  window.webAssistantManagerInitialized = true
  window.webAssistantManager = new WebAssistantManager()
} else {
  console.log('WebAssistantManager: Already initialized, skipping...')
}
