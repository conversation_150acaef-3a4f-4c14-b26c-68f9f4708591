/**
 * DiffModal 消息处理脚本
 * 监听来自 background 的消息，显示 DiffModal 弹窗
 */

import { showDiffModal } from '../components/DiffModal';
import type { TraceData } from '../components/DiffModal/components/Diff/types';

// 监听来自 background 的消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
    // 处理显示 DiffModal 的请求
    if (message.type === 'SHOW_DIFF_MODAL') {
        try {
            const data: TraceData = message.data;

            if (!data) {
                console.error('DiffModal: 缺少必要的数据');
                sendResponse({ success: false, error: '缺少必要的数据' });
                return false;
            }

            // 显示 DiffModal
            showDiffModal(data);

            sendResponse({ success: true });
            return true; // 保持消息通道开放以进行异步响应
        } catch (error) {
            console.error('显示 DiffModal 失败:', error);
            sendResponse({
                success: false,
                error: `显示 DiffModal 失败: ${error instanceof Error ? error.message : String(error)}`
            });
            return false;
        }
    }

    return false; // 对于其他消息不进行处理
});

