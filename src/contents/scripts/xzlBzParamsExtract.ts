/**
 * 新涨乐标注参数提取功能
 * 通过悬浮球中的 action 触发，完成以下两个步骤：
 * 1. 提取页面中的 traceId
 * 2. 发起 API 请求并提取关键参数
 */

// 类型定义
interface TraceIdExtractionResult {
  success: boolean;
  traceId?: string;
  error?: string;
}

interface WorkflowApiResponse {
  code: string;
  message?: string;
  resultData?: Array<Array<{
    workflowId: string;
    workflowInstanceId: string;
    startTime: string; // 添加startTime字段
    states?: Array<{
      stateId: string;
      stateName: string;
      visualName: string;
      visualDesc: string | null;
      stateType: string;
      startTime: string;
      endTime: string;
      startTimeStamp: number;
      endTimeStamp: number;
      duration: number;
      status: string;
      input: string;
      output: string;
      branches: any[];
      downgrade: boolean;
      workflowInstanceId: string;
      componentType: string;
      upstreamLogId: string | null;
      extInfo: any;
    }>;
    [key: string]: any;
  }>>;
}

interface WorkflowDetailRequest {
  traceId: string;
  workflowId: string;
  workflowInstanceId: string;
  startTimeStr: string;
}

interface WorkflowDetailResponse {
  code: string;
  message?: string;
  resultData?: {
    traceId: string;
    nodeType: string;
    name: string;
    duration: number;
    version: string;
    workflowId: string;
    workflowName: string;
    workflowInstanceId: string;
    status: string;
    startTime: string;
    endTime: string;
    input: string;
    output: string;
    reporterIp: string;
    keyInfo: any;
    chainId: string;
    states?: Array<{
      stateId: string;
      stateName: string;
      visualName: string;
      visualDesc: string | null;
      stateType: string;
      startTime: string;
      endTime: string;
      startTimeStamp: number;
      endTimeStamp: number;
      duration: number;
      status: string;
      input: string;
      output: string;
      branches: any[];
      downgrade: boolean;
      workflowInstanceId: string;
      componentType: string;
      upstreamLogId: string | null;
      extInfo: any;
    }>;
    [key: string]: any;
  };
}

interface ExtractedParams {
  workflowId: string;
  workflowInstanceId: string;
  startTime: string; // 添加startTime字段
}

interface ParamExtractionResult {
  success: boolean;
  params?: ExtractedParams;
  workflowData?: any;
  error?: string;
}

// 步骤3相关类型定义
interface NodeDetailRequest {
  traceId: string;
  workflowInstanceId: string;
  name: string;
  startTime: number; // 保持为number类型
  workflowId: string;
}

interface NodeDetailResponse {
  code: string;
  message?: string;
  resultData?: {
    input: string;  // JSON字符串格式
    output: string; // JSON字符串格式
    keyInfos: any[];
    [key: string]: any;
  };
}

interface NodeDetailResult {
  success: boolean;
  data?: {
    input: any;    // 解析后的JSON对象
    output: any;   // 解析后的JSON对象
    keyInfos: any[];
  };
  error?: string;
}

// 步骤4相关类型定义
interface LocalStorageConfig {
  success: boolean;
  config?: string[]; // 一维数组
  error?: string;
}

// 步骤5相关类型定义
interface ExtractedFieldsResult {
  success: boolean;
  extractedFields?: { [key: string]: any };
  error?: string;
}

// 步骤6相关类型定义
interface BackgroundMessageData {
  type: 'XZL_PARAM_EXTRACTION_RESULT_TO_SIDEPANEL';
  data: {
    success: boolean;
    extractedFields?: { [key: string]: any };
    error?: string;
    metadata?: {
      url?: string;
      title?: string;
      traceId?: string;
      workflowId?: string;
      workflowInstanceId?: string;
      timestamp?: number;
    }
  }
}

/**
 * 步骤1: 提取页面中的 traceId
 * 在当前页面的 DOM 中查找所有 class 属性包含 `sprite-descriptions-item-content` 的元素
 * 获取这些元素的文本内容，筛选出以 "light-" 开头的文本值
 */
export function extractTraceId(): TraceIdExtractionResult {
  try {
    console.log('开始提取 traceId...');

    // 检查document是否存在
    // if (typeof document === 'undefined') {
    //   return {
    //     success: false,
    //     error: '无法访问页面文档对象'
    //   };
    // }

    // 查找所有包含指定 class 的元素
    const elements = document.querySelectorAll('[class*="sprite-descriptions-item-content"]');
    console.log(`找到 ${elements.length} 个包含 sprite-descriptions-item-content 的元素`);

    if (elements.length === 0) {
      return {
        success: false,
        error: '未找到包含 sprite-descriptions-item-content 的元素'
      };
    }

    // 遍历元素，查找以 "light-" 开头的文本内容
    for (let i = 0; i < elements.length; i++) {
      const element = elements[i];
      const textContent = element.textContent?.trim();

      if (textContent && textContent.startsWith('light-')) {
        console.log(`找到符合条件的 traceId: ${textContent}`);
        return {
          success: true,
          traceId: textContent
        };
      }
    }

    return {
      success: false,
      error: '未找到以 "light-" 开头的 traceId'
    };

  } catch (error) {
    console.error('提取 traceId 时发生错误:', error);
    // 过滤掉'document is not defined'的错误消息
    const errorMessage = error instanceof Error ? error.message : String(error);
    if (errorMessage.includes('document is not defined')) {
      return {
        success: false,
        error: '无法访问页面文档对象'
      };
    }
    return {
      success: false,
      error: `提取 traceId 失败: ${errorMessage}`
    };
  }
}

/**
 * 步骤2: 发起 API 请求并提取关键参数
 * 使用步骤1获取的 traceId 发起 POST 请求，从响应结果中提取 workflowId 和 workflowInstanceId
 */
export async function extractWorkflowParams(traceId: string): Promise<ParamExtractionResult> {
  try {
    console.log(`开始发起 API 请求，traceId: ${traceId}`);

    const requestBody = {
      traceId: traceId,
      workflowInstanceId: ""
    };

    const response = await fetch('/ht_common_service/orch-bff/fst-orchestration-operation/observe/workflowList', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'env': 'prod'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP 请求失败: ${response.status} ${response.statusText}`);
    }

    const data: WorkflowApiResponse = await response.json();
    console.log('API 响应数据:', data);

    // 验证响应的 code 字段是否为 "0" (表示成功)
    if (data.code !== "0") {
      return {
        success: false,
        error: `API 请求失败: code=${data.code}, message=${data.message || '未知错误'}`
      };
    }

    // 验证响应数据结构
    if (!data.resultData || !Array.isArray(data.resultData) || data.resultData.length === 0) {
      return {
        success: false,
        error: 'API 响应数据格式异常: resultData 为空或不是数组'
      };
    }

    if (!Array.isArray(data.resultData[0]) || data.resultData[0].length === 0) {
      return {
        success: false,
        error: 'API 响应数据格式异常: resultData[0] 为空或不是数组'
      };
    }

    const workflowData = data.resultData[0][0];
    if (!workflowData) {
      return {
        success: false,
        error: 'API 响应数据格式异常: resultData[0][0] 为空'
      };
    }

    // 提取关键参数
    const { workflowId, workflowInstanceId, startTime } = workflowData;

    if (!workflowId || !workflowInstanceId || !startTime) {
      return {
        success: false,
        error: `关键参数缺失: workflowId=${workflowId}, workflowInstanceId=${workflowInstanceId}, startTime=${startTime}`
      };
    }

    console.log(`成功提取参数: workflowId=${workflowId}, workflowInstanceId=${workflowInstanceId}, startTime=${startTime}`);

    return {
      success: true,
      params: {
        workflowId,
        workflowInstanceId,
        startTime // 返回startTime
      },
      // 返回完整的工作流数据，包括状态信息
      workflowData: workflowData
    };

  } catch (error) {
    console.error('发起 API 请求时发生错误:', error);
    return {
      success: false,
      error: `API 请求失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 步骤3: 获取节点详情数据
 * 使用 traceId、workflowId 和 workflowInstanceId 发起第二个 API 请求
 * 根据 localStorage 配置为每个节点ID发起请求
 */
export async function fetchNodeDetails(
  traceId: string,
  workflowId: string,
  workflowInstanceId: string,
  startTime: string, // 添加startTime参数
  workflowStates: any[] // 工作流状态数据
): Promise<{ success: boolean; nodeDetails?: { [name: string]: NodeDetailResult }; error?: string }> {
  try {
    console.log('开始获取节点详情数据...');
    console.log(`参数: traceId=${traceId}, workflowId=${workflowId}, workflowInstanceId=${workflowInstanceId}, startTime=${startTime}`);

    // 第一步：读取本地存储配置
    const configResult = readLocalStorageConfig();
    if (!configResult.success || !configResult.config) {
      return {
        success: false,
        error: `读取配置失败: ${configResult.error}`
      };
    }

    console.log('读取到的配置数组:', configResult.config);

    // 第二步：解析并提取节点ID
    const nodeIds = new Set<string>(); // 使用Set去重
    for (let i = 0; i < configResult.config.length; i++) {
      const configItem = configResult.config[i];
      console.log(`处理配置项 ${i}: ${configItem}`);

      // 从每个字符串中提取第一个井号(#)之前的部分作为节点ID
      const firstHashIndex = configItem.indexOf('#');
      if (firstHashIndex === -1) {
        console.warn(`配置项格式不正确，缺少井号分隔符: ${configItem}`);
        continue;
      }

      const nodeId = configItem.substring(0, firstHashIndex);
      nodeIds.add(nodeId);
      console.log(`从配置项 "${configItem}" 提取节点ID: "${nodeId}"`);
    }

    const uniqueNodeIds = Array.from(nodeIds);
    console.log(`提取到的唯一节点ID列表:`, uniqueNodeIds);

    // 第三步：获取节点详情数据
    const nodeDetails: { [name: string]: NodeDetailResult } = {};

    // 将startTime从"YYYY-MM-DD HH:mm:ss"格式转换为毫秒级时间戳
    const startTimeTimestamp = new Date(startTime).getTime();
    console.log(`转换后的startTime时间戳: ${startTimeTimestamp}`);

    for (let i = 0; i < configResult.config.length; i++) {
      const configItem = configResult.config[i];
      console.log(`处理配置项 ${i}: ${configItem}`);

      // 解析配置项
      const firstHashIndex = configItem.indexOf('#');
      if (firstHashIndex === -1) {
        console.warn(`配置项格式不正确，缺少井号分隔符: ${configItem}`);
        continue;
      }

      const nodeIdPath = configItem.substring(0, firstHashIndex);
      const nodeIdParts = nodeIdPath.split('.');
      const nodeId = nodeIdParts[0];

      console.log(`从配置项 "${configItem}" 提取节点ID路径: "${nodeIdPath}"`);
      try {
        // 根据节点ID路径获取节点详情
        const nodeDetailResult = await fetchNodeDetailByPath(
          traceId,
          workflowId,
          workflowInstanceId,
          startTime,
          startTimeTimestamp,
          nodeIdPath,
          workflowStates
        );
        nodeDetails[nodeIdPath] = nodeDetailResult;

        if (nodeDetailResult.success) {
          console.log(`成功获取节点详情: ${nodeIdPath}`);
        } else {
          console.warn(`获取节点详情失败: ${nodeIdPath}, 错误: ${nodeDetailResult.error}`);
        }

      } catch (error) {
        console.error(`获取节点详情异常: ${nodeIdPath}`, error);
        nodeDetails[nodeIdPath] = {
          success: false,
          error: `请求异常: ${error instanceof Error ? error.message : String(error)}`
        };
      }
    }

    console.log(`完成所有节点详情请求，共 ${Object.keys(nodeDetails).length} 个节点`);

    return {
      success: true,
      nodeDetails
    };

  } catch (error) {
    console.error('获取节点详情数据时发生错误:', error);
    return {
      success: false,
      error: `获取节点详情失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 根据节点路径获取节点详情
 * 支持层级化的节点ID路径，例如: "Local_allInfo_postProcess_rjwwlu.RefWorkflow_1dxqxo.processor_query_for_stock"
 */
async function fetchNodeDetailByPath(
  traceId: string,
  workflowId: string,
  workflowInstanceId: string,
  startTime: string,
  startTimeTimestamp: number,
  nodeIdPath: string,
  workflowStates: any[]
): Promise<NodeDetailResult> {
  try {
    console.log(`开始处理节点路径: ${nodeIdPath}`);

    // 解析节点路径
    const nodeIdParts = nodeIdPath.split('.');
    console.log(`节点路径解析结果:`, nodeIdParts)

    // 如果只有一级节点，直接调用原始方法
    if (nodeIdParts.length === 1) {
      return await fetchSingleNodeDetail(traceId, workflowId, workflowInstanceId, nodeIdParts[0], startTimeTimestamp);
    }

    // 处理层级节点
    let currentWorkflowId = workflowId;
    let currentWorkflowInstanceId = workflowInstanceId;
    let currentStartTime = startTime;
    let _currentState = workflowStates || [];

    // 遍历节点路径中的每一级
    for (let i = 0; i < nodeIdParts.length; i++) {
      const part = nodeIdParts[i];

      console.log(`在状态数组中查找: ${part}`, _currentState);
      const _statu = _currentState?.find((state: any) => {
        return state.stateName == part || state.name == part;
      });
      console.log(`查找结果:`, _statu);

      if (!_statu) {
        console.error(`未找到状态: ${part}，在当前状态列表中`);
        console.log(`当前状态列表中的所有stateName:`, _currentState?.map((s: any) => s.stateName));
        throw new Error(`未找到状态: ${part}`);
      }
      // 更新工作流参数

      if (i === nodeIdParts.length - 1) {
        if (_statu.componentType === 'refWorkflow') {
          // refWorkflow 时，WorkflowId 使用全局的 workflowId;currentWorkflowInstanceId保持父级的不变
          currentWorkflowId = workflowId
          // currentWorkflowInstanceId = _statu.workflowInstanceId;
          currentStartTime = _statu.startTime || currentStartTime;
        } else {
          currentWorkflowId = _statu.extInfo?.workflowId || _statu.workflowId || currentWorkflowId;
          currentWorkflowInstanceId = _statu.workflowInstanceId;
          currentStartTime = _statu.startTime || currentStartTime;
        }
        // 最后一级节点，调用节点详情接口
        console.log(`调用节点详情接口:`, { traceId, currentWorkflowId, currentWorkflowInstanceId, part, startTimeTimestamp });
        return await fetchSingleNodeDetail(traceId, currentWorkflowId, currentWorkflowInstanceId, part, startTimeTimestamp);
      } else {
        currentWorkflowId = _statu.extInfo?.workflowId || _statu.workflowId || currentWorkflowId;
        currentWorkflowInstanceId = _statu.workflowInstanceId;
        currentStartTime = _statu.startTime || currentStartTime;
        // 中间节点，调用工作流详情接口
        console.log(`调用工作流详情接口:`, { traceId, currentWorkflowId, currentWorkflowInstanceId, currentStartTime });
        const workflowDetailResult = await fetchWorkflowDetail(
          traceId,
          currentWorkflowId,
          currentWorkflowInstanceId,
          currentStartTime
        );

        if (!workflowDetailResult.success || !workflowDetailResult.data) {
          throw new Error(`获取工作流详情失败: ${workflowDetailResult.error}`);
        }

        // 在返回的状态中查找下一个节点
        if (_statu?.extInfo?.toolList) {
          _currentState = JSON.parse(_statu.extInfo?.toolList);
        } else {
          _currentState = workflowDetailResult.data.states || [];
        }
        console.log(`获取到下一级状态列表，包含 ${_currentState.length} 个状态`);
      }
    }

    // 理论上不会执行到这里
    throw new Error('节点路径处理异常');

  } catch (error) {
    console.error(`处理节点路径 ${nodeIdPath} 时发生错误:`, error);
    return {
      success: false,
      error: `处理节点路径失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 获取工作流详情
 */
async function fetchWorkflowDetail(
  traceId: string,
  workflowId: string,
  workflowInstanceId: string,
  startTimeStr: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    const requestBody: WorkflowDetailRequest = {
      traceId,
      workflowId,
      workflowInstanceId,
      startTimeStr
    };

    console.log('发起工作流详情请求:', requestBody);

    const response = await fetch('/ht_common_service/orch-bff/fst-orchestration-operation/observe/workflow', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'env': 'prod'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP 请求失败: ${response.status} ${response.statusText}`);
    }

    const data: WorkflowDetailResponse = await response.json();
    console.log('工作流详情 API 响应:', data);

    // 验证响应的 code 字段
    if (data.code !== "0") {
      return {
        success: false,
        error: `API 请求失败: code=${data.code}, message=${data.message || '未知错误'}`
      };
    }

    // 验证响应数据结构
    if (!data.resultData) {
      return {
        success: false,
        error: 'API 响应数据格式异常: resultData 为空'
      };
    }

    return {
      success: true,
      data: data.resultData
    };

  } catch (error) {
    console.error('发起工作流详情请求时发生错误:', error);
    return {
      success: false,
      error: `API 请求失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 发起单个节点详情请求
 */
async function fetchSingleNodeDetail(
  traceId: string,
  workflowId: string,
  workflowInstanceId: string,
  name: string,
  startTime: number // 添加startTime参数
): Promise<NodeDetailResult> {
  try {
    const requestBody: NodeDetailRequest = {
      traceId,
      workflowInstanceId,
      name,
      startTime, // 使用传入的时间戳
      workflowId
    };

    console.log(`发起节点详情请求: ${name}`, requestBody);

    const response = await fetch('/ht_common_service/orch-bff/fst-orchestration-operation/observe/nodeDetail', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'env': 'prod'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP 请求失败: ${response.status} ${response.statusText}`);
    }

    const data: NodeDetailResponse = await response.json();
    console.log(`节点详情 API 响应 (${name}):`, data);

    // 验证响应的 code 字段
    if (data.code !== "0") {
      return {
        success: false,
        error: `API 请求失败: code=${data.code}, message=${data.message || '未知错误'}`
      };
    }

    // 验证响应数据结构
    if (!data.resultData) {
      return {
        success: false,
        error: 'API 响应数据格式异常: resultData 为空'
      };
    }

    // 解析 input 和 output JSON 字符串
    let parsedInput: any = null;
    let parsedOutput: any = null;

    if (data.resultData.input) {
      try {
        parsedInput = JSON.parse(data.resultData.input);
        console.log(`成功解析 input JSON (${name}):`, parsedInput);
      } catch (parseError) {
        console.warn(`解析 input JSON 失败 (${name}):`, parseError);
        parsedInput = data.resultData.input; // 保留原始字符串
      }
    }

    if (data.resultData.output) {
      try {
        parsedOutput = JSON.parse(data.resultData.output);
        console.log(`成功解析 output JSON (${name}):`, parsedOutput);
      } catch (parseError) {
        console.warn(`解析 output JSON 失败 (${name}):`, parseError);
        parsedOutput = data.resultData.output; // 保留原始字符串
      }
    }

    return {
      success: true,
      data: {
        input: parsedInput,
        output: parsedOutput,
        keyInfos: data.resultData.keyInfos || []
      }
    };

  } catch (error) {
    console.error(`发起节点详情请求时发生错误 (${name}):`, error);
    return {
      success: false,
      error: `API 请求失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 步骤4: 从 localStorage 读取字段配置
 * 读取 localStorage 中的 xzl_biaozhu_config 字段
 */
export function readLocalStorageConfig(): LocalStorageConfig {
  try {
    console.log('开始从 localStorage 读取字段配置...');

    // 读取 localStorage 中的配置
    const configStr = localStorage.getItem('xzl_biaozhu_config');

    if (!configStr) {
      return {
        success: false,
        error: 'localStorage 中不存在 xzl_biaozhu_config 字段'
      };
    }

    console.log('读取到的配置字符串:', configStr);

    // 解析 JSON
    let config: any;
    try {
      config = JSON.parse(configStr);
    } catch (parseError) {
      return {
        success: false,
        error: `配置字段不是有效的 JSON: ${parseError instanceof Error ? parseError.message : String(parseError)}`
      };
    }

    // 验证是否为数组
    if (!Array.isArray(config)) {
      return {
        success: false,
        error: '配置字段不是数组格式'
      };
    }

    // 检查数组是否为空
    if (config.length === 0) {
      return {
        success: false,
        error: '配置数组为空'
      };
    }

    // 验证配置是否为字符串数组
    if (!config.every(item => typeof item === 'string')) {
      return {
        success: false,
        error: '配置数组必须是字符串数组'
      };
    }

    console.log(`成功读取配置，包含 ${config.length} 个配置项`);
    console.log('配置内容:', config);

    return {
      success: true,
      config: config // 直接返回一维数组
    };

  } catch (error) {
    console.error('读取 localStorage 配置时发生错误:', error);
    return {
      success: false,
      error: `读取配置失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 步骤5: 根据配置提取数据
 * 根据配置路径从节点详情数据中提取字段值
 */
export function extractFieldsByConfig(
  nodeDetails: { [name: string]: NodeDetailResult },
  config: string[]
): ExtractedFieldsResult {
  try {
    console.log('开始根据配置提取数据...');
    console.log('节点详情数据:', Object.keys(nodeDetails));
    console.log('配置路径:', config, config.length);

    const extractedFields: { [key: string]: any } = {};

    // 第四步：提取目标字段值
    // 遍历配置路径数组（一维数组）
    for (let i = 0; i < config.length; i++) {
      const fieldPath = config[i];
      console.log(`处理字段路径: ${fieldPath}`, i, config, config[i]);

      try {
        const extractedValue = extractSingleField(nodeDetails, fieldPath);
        extractedFields[fieldPath] = extractedValue;

        if (extractedValue !== undefined && extractedValue !== null) {
          console.log(`成功提取字段 ${fieldPath}:`, extractedValue);
        } else {
          console.warn(`字段 ${fieldPath} 的值为空或未找到`);
        }

      } catch (error) {
        console.warn(`提取字段 ${fieldPath} 时发生错误:`, error);
        extractedFields[fieldPath] = null;
      }
    }

    console.log(`完成字段提取，共提取 ${Object.keys(extractedFields).length} 个字段`);
    console.log('提取结果:', extractedFields);

    return {
      success: true,
      extractedFields
    };

  } catch (error) {
    console.error('根据配置提取数据时发生错误:', error);
    return {
      success: false,
      error: `字段提取失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 提取单个字段的值
 * 从节点详情数据中根据字段路径提取对应的值
 */
function extractSingleField(nodeDetails: { [name: string]: NodeDetailResult }, fieldPath: string): any {
  // 解析字段路径，例如: "RefWorkflow_audit_question#outPut.auditMessage"
  const hashIndex = fieldPath.indexOf('#');
  if (hashIndex === -1) {
    console.warn(`字段路径格式不正确: ${fieldPath}，期望格式: nodeId#input/outPut.fieldPath`);
    return null;
  }

  const nodeId = fieldPath.substring(0, hashIndex);
  const remainingPathStr = fieldPath.substring(hashIndex + 1);
  const remainingParts = remainingPathStr.split('.');

  if (remainingParts.length < 2) {
    console.warn(`字段路径格式不正确: ${fieldPath}，期望格式: nodeId#input/outPut.fieldPath`);
    return null;
  }

  const dataType = remainingParts[0]; // "input" 或 "outPut"
  const remainingPath = remainingParts.slice(1); // 剩余的嵌套路径，例如: ["auditMessage"]

  console.log(`解析字段路径: nodeId=${nodeId}, dataType=${dataType}, remainingPath=${remainingPath.join('.')}`, nodeDetails);

  // 检查节点是否存在
  if (!nodeDetails[nodeId]) {
    console.warn(`节点 ${nodeId} 不存在于节点详情数据中`);
    return null;
  }

  const nodeDetail = nodeDetails[nodeId];
  if (!nodeDetail.success || !nodeDetail.data) {
    console.warn(`节点 ${nodeId} 的数据获取失败或为空`);
    return null;
  }

  // 确定数据源 (input 或 output)
  let dataSource: any;
  if (dataType.toLowerCase() === 'input') {
    dataSource = nodeDetail.data.input;
  } else if (dataType.toLowerCase() === 'output') {
    dataSource = nodeDetail.data.output;
  } else {
    console.warn(`不支持的数据类型: ${dataType}，仅支持 input 或 output`);
    return null;
  }

  if (!dataSource) {
    console.warn(`节点 ${nodeId} 的 ${dataType} 数据为空`);
    return null;
  }

  // 根据剩余路径提取嵌套字段值
  return extractNestedValue(dataSource, remainingPath);
}

/**
 * 从嵌套对象中提取值，支持对象和数组访问（如 items.0.name）
 * 修改后支持模糊匹配：当pathParts为[a,b,c]时，如果a.b不存在，
 * 则会在a的所有子属性中递归查找是否有包含b的属性，只要有任意一层存在b就返回其值
 */
function extractNestedValue(obj: any, pathParts: string[]): any {
  let current = obj;

  for (let i = 0; i < pathParts.length; i++) {
    const part = pathParts[i];

    // 当前层级为 null 或 undefined，无法继续
    if (current === null || current === undefined) {
      console.warn(`路径中断: 在访问 ${part} 时，当前值为 null 或 undefined`);
      return null;
    }

    // 非对象且非数组，无法继续按属性访问
    if (typeof current !== 'object') {
      console.warn(`路径中断: 在访问 ${part} 时，当前值不是对象或数组:`, typeof current);
      return null;
    }

    // 特殊处理：如果 part 是数字且 current 是数组，则按索引访问
    if (Array.isArray(current) && /^\d+$/.test(part)) {
      const index = parseInt(part, 10);
      if (index < 0 || index >= current.length) {
        console.warn(`数组索引越界: ${part}`);
        return null;
      }
      current = current[index];
      continue;
    }

    // 正常对象属性访问
    if (part in current) {
      current = current[part];
      continue;
    }

    // 新增逻辑：如果当前属性不存在，则在当前对象的所有属性中递归深度查找
    let found = false;
    function deepSearch(target: any): any {
      if (!target || typeof target !== 'object') {
        return undefined;
      }

      // 检查当前对象是否包含目标属性
      if (part in target) {
        return target[part];
      }

      // 递归搜索所有子属性
      for (const key in target) {
        if (target[key] && typeof target[key] === 'object') {
          const result = deepSearch(target[key]);
          if (result !== undefined) {
            return result;
          }
        }
      }

      return undefined;
    }

    const deepSearchResult = deepSearch(current);
    if (deepSearchResult !== undefined) {
      current = deepSearchResult;
      found = true;
    }

    // 如果在深层找到了目标属性，继续下一轮循环
    if (found) {
      continue;
    }

    console.warn(`字段 ${part} 不存在于当前对象中`);
    return null;
  }

  return current;
}


/**
 * 步骤6: 发送数据到后台脚本
 * 使用 chrome.runtime.sendMessage 将提取的数据发送到 background script
 */
export async function sendDataToBackground(
  extractedFields: { [key: string]: any },
  metadata: {
    traceId: string;
    workflowId: string;
    workflowInstanceId: string;
    timestamp: number;
  }
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('开始发送数据到后台脚本...');
    console.log('提取的字段数据:', extractedFields);
    console.log('元数据:', metadata);

    const message: BackgroundMessageData = {
      type: 'XZL_PARAM_EXTRACTION_RESULT_TO_SIDEPANEL',
      data: {
        success: true,
        extractedFields,
        metadata
      }
    };

    console.log('发送消息到 side panel:', message);

    // 发送消息到 side panel
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, (_response) => {
        if (chrome.runtime.lastError) {
          console.error('发送数据到 side panel 失败:', chrome.runtime.lastError);
          resolve({
            success: false,
            error: `发送数据失败: ${chrome.runtime.lastError.message}`
          });
        } else {
          console.log('数据已发送到 side panel');
          resolve({
            success: true
          });
        }
      });
    });

  } catch (error) {
    console.error('发送数据到后台脚本时发生错误:', error);
    return {
      success: false,
      error: `发送数据失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 发送错误信息到后台脚本
 */
export async function sendErrorToBackground(
  error: string,
  metadata?: {
    traceId?: string;
    workflowId?: string;
    workflowInstanceId?: string;
    timestamp: number;
  }
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('发送错误信息到后台脚本:', error);

    const message: BackgroundMessageData = {
      type: 'XZL_PARAM_EXTRACTION_RESULT_TO_SIDEPANEL',
      data: {
        success: false,
        error,
        metadata
      }
    };

    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, (_response) => {
        if (chrome.runtime.lastError) {
          console.error('发送错误消息到 background script 失败:', chrome.runtime.lastError);
          resolve({
            success: false,
            error: `发送错误消息失败: ${chrome.runtime.lastError.message}`
          });
        } else {
          console.log('成功发送错误消息到 background script');
          resolve({
            success: true
          });
        }
      });
    });

  } catch (sendError) {
    console.error('发送错误信息时发生异常:', sendError);
    return {
      success: false,
      error: `发送错误信息失败: ${sendError instanceof Error ? sendError.message : String(sendError)}`
    };
  }
}

/**
 * 主函数: 执行完整的新涨乐标注参数提取流程（6个步骤）
 * 这是对外暴露的主要接口，会被悬浮球的 action 调用
 *
 * 重构后的流程：
 * 1. 第一步：读取本地存储配置（一维字符串数组）
 * 2. 第二步：解析并提取节点ID（从每个配置项的第一个井号之前提取）
 * 3. 第三步：获取节点详情数据（根据提取出的节点ID列表）
 * 4. 第四步：提取目标字段值（根据原始配置字符串中井号后面的路径）
 */
export async function executeXzlParamExtraction(): Promise<{
  success: boolean;
  data?: {
    traceId: string;
    workflowId: string;
    workflowInstanceId: string;
    startTime: string;
    extractedFields: { [key: string]: any };
    timestamp: number;
  };
  error?: string;
}> {
  const timestamp = Date.now();

  try {
    console.log('='.repeat(60));
    console.log('开始执行新涨乐标注参数提取流程（6个步骤）...');
    console.log('='.repeat(60));

    // 步骤1: 提取 traceId
    console.log('步骤1: 提取页面中的 traceId');
    const traceIdResult = extractTraceId();
    if (!traceIdResult.success || !traceIdResult.traceId) {
      console.error('步骤1失败 - 提取 traceId 失败:', traceIdResult.error);
      await sendErrorToBackground(traceIdResult.error || '提取 traceId 失败', { timestamp });
      return {
        success: false,
        error: traceIdResult.error || '提取 traceId 失败'
      };
    }
    console.log('步骤1成功 - traceId:', traceIdResult.traceId);

    // 步骤2: 发起第一个 API 请求并提取工作流参数
    console.log('步骤2: 发起 API 请求获取工作流参数');
    const paramsResult = await extractWorkflowParams(traceIdResult.traceId);
    if (!paramsResult.success || !paramsResult.params) {
      console.error('步骤2失败 - 提取工作流参数失败:', paramsResult.error);
      await sendErrorToBackground(paramsResult.error || '提取工作流参数失败', {
        traceId: traceIdResult.traceId,
        timestamp
      });
      return {
        success: false,
        error: paramsResult.error || '提取工作流参数失败'
      };
    }
    console.log('步骤2成功 - workflowId:', paramsResult.params.workflowId, 'workflowInstanceId:', paramsResult.params.workflowInstanceId, 'startTime:', paramsResult.params.startTime);

    // 步骤3: 获取节点详情数据
    console.log('步骤3: 获取节点详情数据');
    const nodeDetailsResult = await fetchNodeDetails(
      traceIdResult.traceId,
      paramsResult.params.workflowId,
      paramsResult.params.workflowInstanceId,
      paramsResult.params.startTime, // 传递startTime参数
      paramsResult.workflowData?.states || [] // 传递工作流状态数据
    );
    if (!nodeDetailsResult.success || !nodeDetailsResult.nodeDetails) {
      console.error('步骤3失败 - 获取节点详情失败:', nodeDetailsResult.error);
      await sendErrorToBackground(nodeDetailsResult.error || '获取节点详情失败', {
        traceId: traceIdResult.traceId,
        workflowId: paramsResult.params.workflowId,
        workflowInstanceId: paramsResult.params.workflowInstanceId,
        timestamp
      });
      return {
        success: false,
        error: nodeDetailsResult.error || '获取节点详情失败'
      };
    }
    console.log('步骤3成功 - 获取到', Object.keys(nodeDetailsResult.nodeDetails).length, '个节点的详情数据');

    // 步骤4: 从 localStorage 读取字段配置（已在步骤3中调用）
    console.log('步骤4: 读取字段配置（已在步骤3中完成）');

    // 步骤5: 根据配置提取数据
    console.log('步骤5: 根据配置提取字段数据');
    const configResult = readLocalStorageConfig();
    if (!configResult.success || !configResult.config) {
      console.error('步骤5失败 - 读取配置失败:', configResult.error);
      await sendErrorToBackground(configResult.error || '读取配置失败', {
        traceId: traceIdResult.traceId,
        workflowId: paramsResult.params.workflowId,
        workflowInstanceId: paramsResult.params.workflowInstanceId,
        timestamp
      });
      return {
        success: false,
        error: configResult.error || '读取配置失败'
      };
    }

    const extractResult = extractFieldsByConfig(nodeDetailsResult.nodeDetails, configResult.config);
    if (!extractResult.success || !extractResult.extractedFields) {
      console.error('步骤5失败 - 字段提取失败:', extractResult.error);
      await sendErrorToBackground(extractResult.error || '字段提取失败', {
        traceId: traceIdResult.traceId,
        workflowId: paramsResult.params.workflowId,
        workflowInstanceId: paramsResult.params.workflowInstanceId,
        timestamp
      });
      return {
        success: false,
        error: extractResult.error || '字段提取失败'
      };
    }
    console.log('步骤5成功 - 提取到', Object.keys(extractResult.extractedFields).length, '个字段', extractResult);

    // 步骤6: 发送数据到后台脚本
    console.log('步骤6: 发送数据到后台脚本');
    const sendResult = await sendDataToBackground(extractResult.extractedFields, {
      traceId: traceIdResult.traceId,
      workflowId: paramsResult.params.workflowId,
      workflowInstanceId: paramsResult.params.workflowInstanceId,
      timestamp
    });

    if (!sendResult.success) {
      console.warn('步骤6警告 - 发送数据到后台脚本失败:', sendResult.error);
      // 注意：这里不返回失败，因为数据提取已经成功，只是发送失败
    } else {
      console.log('步骤6成功 - 数据已发送到后台脚本');
    }

    const result = {
      success: true,
      data: {
        traceId: traceIdResult.traceId,
        workflowId: paramsResult.params.workflowId,
        workflowInstanceId: paramsResult.params.workflowInstanceId,
        startTime: paramsResult.params.startTime, // 添加startTime到返回结果
        extractedFields: extractResult.extractedFields,
        timestamp
      }
    };

    console.log('='.repeat(60));
    console.log('新涨乐标注参数提取流程完成！');
    console.log('提取结果摘要:');
    console.log('- traceId:', result.data.traceId);
    console.log('- workflowId:', result.data.workflowId);
    console.log('- workflowInstanceId:', result.data.workflowInstanceId);
    console.log('- startTime:', result.data.startTime);
    console.log('- 提取字段数量:', Object.keys(result.data.extractedFields).length);
    console.log('- 时间戳:', result.data.timestamp);
    console.log('='.repeat(60));

    return result;

  } catch (error) {
    console.error('执行新涨乐标注参数提取时发生未预期的错误:', error);
    const errorMessage = `执行失败: ${error instanceof Error ? error.message : String(error)}`;

    // 尝试发送错误信息到后台脚本
    try {
      await sendErrorToBackground(errorMessage, { timestamp });
    } catch (sendError) {
      console.error('发送错误信息到后台脚本也失败了:', sendError);
    }

    return {
      success: false,
      error: errorMessage
    };
  }
}

// 添加消息监听器，处理来自background的消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  // 处理来自background的参数提取请求
  if (message.type === 'XZL_PARAM_EXTRACTION_REQUEST_FROM_SIDEPANEL') {
    // 执行参数提取
    executeXzlParamExtraction().then((result) => {
      sendResponse(result);
    }).catch((error) => {
      console.error('执行参数提取时发生错误:', error);
      sendResponse({
        success: false,
        error: `执行参数提取时发生错误: ${error instanceof Error ? error.message : String(error)}`
      });
    });
    return true; // 保持消息通道开放以进行异步响应
  }

  return false; // 对于其他消息不进行处理
});
