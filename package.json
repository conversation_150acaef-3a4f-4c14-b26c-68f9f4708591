{"name": "ai-chrome-extension", "displayName": "Web助手", "version": "0.0.8", "description": "Web助手", "contributors": ["019327", "021961"], "scripts": {"dev": "plasmo dev --verbose", "build": "plasmo build --tag=prod", "build:test": "plasmo build --tag=test", "package": "npm run build && plasmo package && node ./scripts/rename-zip.js", "package:test": "npm run build:test && plasmo package --tag=test && node ./scripts/rename-test-zip.js"}, "dependencies": {"@ant-design/cssinjs": "1.18.2", "@ant-design/icons": "^6.0.0", "@ht/chatui": "1.0.2-beta.8", "@ht/xlog": "^4.1.0", "@mozilla/readability": "^0.6.0", "@plasmohq/redux-persist": "^6.1.0", "@plasmohq/storage": "^1.15.0", "@reduxjs/toolkit": "^2.0.1", "ahooks": "^3.9.4", "antd": "5.13.2", "axios": "^1.9.0", "cross-env": "^10.0.0", "html2canvas": "1.4.1", "plasmo": "0.90.5", "react": "18.2.0", "react-dom": "18.2.0", "react-markdown": "^8.0.7", "react-redux": "^9.1.0", "react-router-dom": "^7.6.2", "redux": "^5.0.1", "redux-persist-webextension-storage": "^1.0.2", "rehype-raw": "^7.0.0", "string-width": "^8.0.0", "turndown": "^7.2.1", "turndown-plugin-gfm": "^1.0.2", "webextension-polyfill-ts": "^0.26.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/chrome": "0.0.258", "@types/node": "20.11.5", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "typescript": "5.3.3"}, "resolutions": {"string-width": "^4.2.3"}, "manifest": {"host_permissions": ["<all_urls>"], "permissions": ["storage", "activeTab", "sidePanel", "contextMenus", "scripting", "tabs", "cookies", "alarms", "notifications", "downloads", "webRequest"], "background": {"service_worker": "background.ts"}, "side_panel": {"default_path": "sidepanel.html", "default_width": 500, "minimum_width": 400}, "web_accessible_resources": [{"resources": ["assets/*", "assets/**/*"], "matches": ["<all_urls>"]}]}}