# Browser Tool SDK 页面内容获取改进

## 修改说明

已成功将 `browser-tool-sdk.ts` 中的 `getPageInfo` 方法从简单的文本提取改进为使用 Readability 和 Markdown 转换的高质量内容提取。

## 主要改进

### 1. 导入必要依赖
```typescript
import { convertHtmlToLlmMarkdown } from '@src/sidepanel/utils/html2md'
import { Readability } from '@mozilla/readability'
```

### 2. 新增 convertWithReadability 方法
```typescript
private convertWithReadability(html: string, pageUrl: string): string {
  const htmlContent = convertHtmlToLlmMarkdown(html)
  try {
    const parser = new DOMParser()
    const doc = parser.parseFromString(html || '', 'text/html')
    // 为相对链接提供 base
    if (doc?.head) {
      const base = doc.createElement('base')
      base.href = pageUrl || document.baseURI || ''
      doc.head.appendChild(base)
    }

    const cloned = doc.cloneNode(true) as Document
    const article = new Readability(cloned).parse()
    if (article?.content) {
      const articleContent = convertHtmlToLlmMarkdown(article.content)
      return articleContent
    }
    return htmlContent
  } catch (e) {
    return htmlContent
  }
}
```

### 3. 修改 getPageInfo 方法
- 首先获取完整的 HTML 内容：`'<!DOCTYPE html>\n' + document.documentElement.outerHTML`
- 使用 `convertWithReadability` 处理页面内容，生成高质量的 Markdown 格式文本
- 保持原有的 DOM 结构处理逻辑

## 使用效果

现在 `pageData.text` 将包含：
- 经过 Readability 处理的高质量正文内容
- 转换为 Markdown 格式的结构化文本
- 去除了广告、导航栏等无关内容
- 保留了文章的核心信息和结构

这与总结页面功能中使用的方法完全一致，确保了内容质量的统一性。
