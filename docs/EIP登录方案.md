# EIP登录方案

EIP登录采用基于Cookie认证 + 接口监听 + 状态同步的机制。整个流程是：用户在插件侧面板点击登录，系统打开EIP登录页面（生产环境：https://eiplite.htsc.com.cn，测试环境：http://eipuat.htsc.com.cn），用户完成登录后，Background脚本通过 `chrome.webRequest.onCompleted` 监听到 `/gateway/login` 接口调用完成，然后自动获取浏览器中的 `EIPGW-TOKEN` Cookie，调用 `/portal-xc/auth/loginXC` 接口进行验证，验证成功后将用户信息存储到 `chrome.storage.local`，并通过消息机制同步更新侧面板的UI状态。

状态管理方面，用户信息存储在Chrome Extension的本地存储中，Cookie由浏览器自动管理，整个生命周期包括登录检查、状态更新和登出清理。关键的API包括登录验证接口 `POST /portal-xc/auth/loginXC`，通过Cookie和接口响应来判断登录状态，Background和Sidepanel之间通过Chrome消息机制进行状态同步。

技术实现上使用 `chrome.webRequest.onCompleted` 监听登录接口完成，保存原始Tab ID以便登录后自动切换回来，登录失败时自动清理本地数据，同时支持EIP登录成功后触发Wiki认证。安全方面，Token仅存储在浏览器Cookie中，用户信息加密存储在Extension本地，登出时自动清理所有认证信息。
